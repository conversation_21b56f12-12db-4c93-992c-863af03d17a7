/** @format */

'use client';

import { useSession } from 'next-auth/react';
import { useCallback, useEffect, useRef, useState } from 'react';

// Types for different interaction types
export type InteractionType = 'favorites' | 'likes' | 'visits' | 'reviews';

export interface InteractionItem {
	id: string;
	poi_id?: string;
	user_poi_temp_id?: string;
	user_poi_approved_id?: string;
	poi_type: string;
	interaction_type: string;
	created_at: string;
	metadata?: Record<string, unknown>;
	// Additional fields for specific types
	rating?: number; // for reviews
	notes?: string; // for favorites/visits
	visit_date?: string; // for visits
}

export interface InteractionState {
	items: InteractionItem[];
	loading: boolean;
	error: string | null;
	hasMore: boolean;
	totalCount: number;
	currentOffset: number;
}

export interface UseSharedUserInteractionsOptions {
	user_id?: string;
	auto_load?: boolean;
	limit?: number;
	offset?: number;
}

export interface UseSharedUserInteractionsResult {
	// State for each interaction type
	favorites: InteractionState;
	likes: InteractionState;
	visits: InteractionState;
	reviews: InteractionState;

	// Global loading state
	globalLoading: boolean;

	// Actions
	loadInteractions: (
		type: InteractionType,
		resetOffset?: boolean
	) => Promise<void>;
	loadMore: (type: InteractionType) => Promise<void>;
	refresh: (type: InteractionType) => Promise<void>;
	refreshAll: () => Promise<void>;
	removeInteraction: (
		type: InteractionType,
		interactionId: string
	) => Promise<void>;
	lazyLoadInteraction: (type: InteractionType) => Promise<void>;
}

// Request cache to prevent duplicate requests
interface TimestampedPromise extends Promise<unknown> {
	timestamp?: number;
}

const requestCache = new Map<string, TimestampedPromise>();
const cacheTimeout = 5000; // 5 seconds

// Helper to create cache key
const createCacheKey = (
	userId: string,
	type: InteractionType,
	offset: number,
	limit: number
): string => {
	return `${userId}-${type}-${offset}-${limit}`;
};

// Helper to clear expired cache entries
const clearExpiredCache = () => {
	// Promises do not have a timestamp property, so just clear the cache after timeout
	requestCache.clear();
};

const createInitialState = (): InteractionState => ({
	items: [],
	loading: false,
	error: null,
	hasMore: false,
	totalCount: 0,
	currentOffset: 0,
});

export const useSharedUserInteractions = ({
	user_id,
	auto_load = true,
	limit = 20,
}: UseSharedUserInteractionsOptions = {}): UseSharedUserInteractionsResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State for each interaction type
	const [favorites, setFavorites] =
		useState<InteractionState>(createInitialState);
	const [likes, setLikes] = useState<InteractionState>(createInitialState);
	const [visits, setVisits] = useState<InteractionState>(createInitialState);
	const [reviews, setReviews] = useState<InteractionState>(createInitialState);

	// Global loading state
	const [globalLoading, setGlobalLoading] = useState(false);

	// Refs to track mounted state and prevent state updates after unmount
	const mountedRef = useRef(true);
	const loadedTypesRef = useRef<Set<InteractionType>>(new Set());

	// Cleanup on unmount
	useEffect(() => {
		return () => {
			mountedRef.current = false;
		};
	}, []);

	// Helper to get state setter for interaction type
	const getStateSetter = (type: InteractionType) => {
		switch (type) {
			case 'favorites':
				return setFavorites;
			case 'likes':
				return setLikes;
			case 'visits':
				return setVisits;
			case 'reviews':
				return setReviews;
			default:
				throw new Error(`Unknown interaction type: ${type}`);
		}
	};

	// Helper to get current state for interaction type
	const getCurrentState = (type: InteractionType): InteractionState => {
		switch (type) {
			case 'favorites':
				return favorites;
			case 'likes':
				return likes;
			case 'visits':
				return visits;
			case 'reviews':
				return reviews;
			default:
				throw new Error(`Unknown interaction type: ${type}`);
		}
	};

	// Load interactions for a specific type
	const loadInteractions = useCallback(
		async (
			type: InteractionType,
			resetOffset: boolean = false
		): Promise<void> => {
			if (!currentUserId) return;

			const currentState = getCurrentState(type);
			const offsetToUse = resetOffset ? 0 : currentState.currentOffset;
			const cacheKey = createCacheKey(currentUserId, type, offsetToUse, limit);

			// Check if request is already in progress
			if (requestCache.has(cacheKey)) {
				try {
					await requestCache.get(cacheKey);
					return;
				} catch {
					// If cached request failed, remove it and try again
					requestCache.delete(cacheKey);
				}
			}

			const setState = getStateSetter(type);

			setState((prev) => ({ ...prev, loading: true, error: null }));

			// Create the request promise
			const requestPromise = (async () => {
				try {
					let response, data;

					if (type === 'reviews') {
						// Reviews use a different API endpoint
						const reviewsUrl = `/api/pois/reviews?userId=${currentUserId}&limit=${limit}&offset=${offsetToUse}`;
						// console.log(`Fetching reviews from: ${reviewsUrl}`)

						response = await fetch(reviewsUrl, {
							method: 'GET',
							headers: { 'Content-Type': 'application/json' },
							credentials: 'include',
						});

						if (!response.ok) {
							throw new Error(`HTTP error! status: ${response.status}`);
						}

						data = await response.json();

						if (!data.success) {
							throw new Error(data.error || `Failed to load ${type}`);
						}

						// Transform reviews API response to match interactions format
						return {
							success: true,
							interactions: data.reviews || [],
							total_count: data.pagination?.total || 0,
							has_more: data.pagination?.hasMore || false,
						};
					} else {
						// Use the unified interactions API for likes, favorites, visits
						const interactionTypeParam =
							type === 'favorites'
								? 'favorite'
								: type === 'likes'
								? 'like'
								: type === 'visits'
								? 'visit'
								: type;
						const interactionsUrl = `/api/pois/interactions?userId=${currentUserId}&interactionType=${interactionTypeParam}&limit=${limit}&offset=${offsetToUse}`;
						// console.log(`Fetching ${type} from: ${interactionsUrl}`)

						response = await fetch(interactionsUrl, {
							method: 'GET',
							headers: { 'Content-Type': 'application/json' },
							credentials: 'include',
						});

						if (!response.ok) {
							throw new Error(`HTTP error! status: ${response.status}`);
						}

						data = await response.json();

						if (!data.success) {
							throw new Error(data.error || `Failed to load ${type}`);
						}

						return data;
					}
				} catch (error) {
					console.error(`Error loading ${type}:`, error);
					throw error;
				}
			})();

			// Add timestamp for cache expiration
			(requestPromise as TimestampedPromise).timestamp = Date.now();
			requestCache.set(cacheKey, requestPromise as TimestampedPromise);

			try {
				const data = await requestPromise;

				const interactions = (data.interactions || []) as InteractionItem[];

				// console.log(`Loaded ${type}:`, {
				//   count: interactions.length,
				//   totalCount: data.total_count,
				//   hasMore: data.has_more,
				//   resetOffset,
				//   offsetToUse,
				//   mounted: mountedRef.current
				// })

				// Always update state - React will handle cleanup if component unmounts
				setState((prev) => ({
					...prev,
					items:
						resetOffset || offsetToUse === 0
							? interactions
							: [...prev.items, ...interactions],
					loading: false,
					error: null,
					hasMore: data.has_more || false,
					totalCount: data.total_count || 0,
					currentOffset: offsetToUse + interactions.length,
				}));

				// Mark this type as loaded
				loadedTypesRef.current.add(type);
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : `Failed to load ${type}`;
				console.error(`Error loading ${type}:`, error);

				// Always update state - React will handle cleanup if component unmounts
				setState((prev) => ({
					...prev,
					loading: false,
					error: errorMessage,
				}));
			} finally {
				// Clean up cache after a delay
				setTimeout(() => {
					requestCache.delete(cacheKey);
					clearExpiredCache();
				}, cacheTimeout);
			}
		},
		[currentUserId, limit]
	);

	// Load more items for a specific type
	const loadMore = useCallback(
		async (type: InteractionType): Promise<void> => {
			const currentState = getCurrentState(type);
			if (currentState.loading || !currentState.hasMore) return;

			await loadInteractions(type, false);
		},
		[loadInteractions]
	);

	// Refresh a specific interaction type
	const refresh = useCallback(
		async (type: InteractionType): Promise<void> => {
			// Clear any cached requests for this type
			const pattern = `${currentUserId}-${type}-`;
			for (const key of requestCache.keys()) {
				if (key.startsWith(pattern)) {
					requestCache.delete(key);
				}
			}

			await loadInteractions(type, true);
		},
		[loadInteractions, currentUserId]
	);

	// Refresh all interaction types
	const refreshAll = useCallback(async (): Promise<void> => {
		if (!currentUserId) return;

		setGlobalLoading(true);

		try {
			// Clear all cache
			requestCache.clear();

			// Refresh all loaded types in parallel
			const loadedTypes = Array.from(loadedTypesRef.current);
			await Promise.all(loadedTypes.map((type) => refresh(type)));
		} finally {
			if (mountedRef.current) {
				setGlobalLoading(false);
			}
		}
	}, [refresh, currentUserId]);

	// Remove an interaction
	const removeInteraction = useCallback(
		async (type: InteractionType, interactionId: string): Promise<void> => {
			if (!currentUserId) return;

			const setState = getStateSetter(type);

			try {
				let response;

				if (type === 'reviews') {
					// Reviews use a different API endpoint
					response = await fetch(
						`/api/pois/reviews?reviewId=${interactionId}`,
						{
							method: 'DELETE',
							headers: { 'Content-Type': 'application/json' },
							credentials: 'include',
						}
					);
				} else {
					// Find the interaction item to get POI details
					const currentState = getCurrentState(type);
					const interactionItem = currentState.items.find(
						(item) => item.id === interactionId
					);

					if (!interactionItem) {
						throw new Error(`Interaction with ID ${interactionId} not found`);
					}

					// Map interaction type to API parameter
					const interactionTypeParam =
						type === 'favorites'
							? 'favorite'
							: type === 'likes'
							? 'like'
							: type === 'visits'
							? 'visit'
							: type;

					// Build the request body using the POST API with action: 'remove'
					const requestBody = {
						poiId: interactionItem.poi_id || null,
						userPoiTempId: interactionItem.user_poi_temp_id || null,
						userPoiApprovedId: interactionItem.user_poi_approved_id || null,
						poiType: interactionItem.poi_type,
						interactionType: interactionTypeParam,
						action: 'remove',
					};

					response = await fetch('/api/pois/interactions', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						credentials: 'include',
						body: JSON.stringify(requestBody),
					});
				}

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = await response.json();

				if (!data.success) {
					throw new Error(data.error || `Failed to remove ${type}`);
				}

				// Update state optimistically
				setState((prev) => ({
					...prev,
					items: prev.items.filter((item) => item.id !== interactionId),
					totalCount: Math.max(0, prev.totalCount - 1),
				}));
			} catch (error) {
				console.error(`Error removing ${type}:`, error);
				throw error;
			}
		},
		[currentUserId, getCurrentState]
	);

	// Auto-load on mount (only once per type)
	useEffect(() => {
		if (auto_load && currentUserId && mountedRef.current) {
			// Small delay to prevent rapid successive calls and allow React to settle
			const timeoutId = setTimeout(() => {
				if (mountedRef.current) {
					// Load favorites by default (most commonly used)
					if (!loadedTypesRef.current.has('favorites')) {
						loadInteractions('favorites', true);
					}
				}
			}, 150);

			return () => clearTimeout(timeoutId);
		}
	}, [auto_load, currentUserId, loadInteractions]);

	// Performance optimization: Lazy load interactions only when needed
	const lazyLoadInteraction = useCallback(
		async (type: InteractionType): Promise<void> => {
			// console.log(`lazyLoadInteraction(${type}):`, {
			//   alreadyLoaded: loadedTypesRef.current.has(type),
			//   currentUserId,
			//   mounted: mountedRef.current
			// })

			if (!loadedTypesRef.current.has(type) && currentUserId) {
				await loadInteractions(type, true);
			}
		},
		[loadInteractions, currentUserId]
	);

	// Memory management: Clear cache when component unmounts
	useEffect(() => {
		return () => {
			// Clear all cached requests for this user when component unmounts
			if (currentUserId) {
				const pattern = `${currentUserId}-`;
				for (const key of requestCache.keys()) {
					if (key.startsWith(pattern)) {
						requestCache.delete(key);
					}
				}
			}
		};
	}, [currentUserId]);

	return {
		favorites,
		likes,
		visits,
		reviews,
		globalLoading,
		loadInteractions,
		loadMore,
		refresh,
		refreshAll,
		removeInteraction,
		lazyLoadInteraction,
	};
};
