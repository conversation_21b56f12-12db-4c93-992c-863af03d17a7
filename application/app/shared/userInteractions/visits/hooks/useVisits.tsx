/** @format */

'use client';

import { VisitsService } from '@/app/shared/userInteractions/visits/services';
import {
	POIVisitInteraction,
	UseUserVisitsOptions,
	UseUserVisitsResult,
	UseVisitsOptions,
	UseVisitsResult,
	VisitWithUser,
} from '@/app/shared/userInteractions/visits/types';
import { useSession } from 'next-auth/react';
import { useCallback, useEffect, useState } from 'react';

export const useVisits = ({
	poi_identifier,
	user_id,
	auto_load = true,
	enable_optimistic_updates = true,
	sort_by = 'created_at',
	sort_order = 'desc',
	limit = 20,
}: UseVisitsOptions = {}): UseVisitsResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [visits, setVisits] = useState<VisitWithUser[]>([]);
	const [userVisit, setUserVisit] = useState<POIVisitInteraction | null>(null);
	const [visitCount, setVisitCount] = useState(0);
	const [hasVisited, setHasVisited] = useState(false);
	const [loading, setLoading] = useState(true); // true by default for initial load
	const [actionLoading, setActionLoading] = useState(false);
	const [ready, setReady] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(false);
	const [currentOffset, setCurrentOffset] = useState(0);

	// Load visit state for the POI (simplified like likes)
	const loadVisitState = useCallback(async () => {
		if (!poi_identifier) return;

		setLoading(true);
		setError(null);

		try {
			if (currentUserId) {
				// Load user-specific visit status
				const response = await VisitsService.getUserVisitStatus(
					poi_identifier,
					currentUserId
				);

				if (response.success) {
					setHasVisited(response.hasVisited);
					setVisitCount(response.visitCount || 0);
					setReady(true);
					console.log('Loaded visit state:', {
						hasVisited: response.hasVisited,
						visitCount: response.visitCount,
					});
				} else {
					throw new Error('Failed to load visit status');
				}
			} else {
				// Load public visit count only (no user authentication)
				const response = await VisitsService.getPOIVisitCount(poi_identifier);

				if (response.success) {
					setHasVisited(false); // No user, so not visited
					setVisitCount(response.visitCount || 0);
					setReady(true);
					console.log('Loaded public visit count:', response.visitCount);
				} else {
					throw new Error('Failed to load visit count');
				}
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to load visit data';
			setError(errorMessage);
			console.error('Error loading visit state:', err);
		} finally {
			setLoading(false);
		}
	}, [poi_identifier, currentUserId]);

	// Initial load effect - only load visit details when auto_load is true
	useEffect(() => {
		let cancelled = false;
		if (!poi_identifier) {
			setLoading(false);
			setReady(false);
			setError('POI identifier is missing.');
			return;
		}

		// Validate POI identifier
		if (
			!poi_identifier.poi_type ||
			!['official', 'user_temp', 'user_approved'].includes(
				poi_identifier.poi_type
			)
		) {
			setLoading(false);
			setReady(false);
			setError('Invalid POI type.');
			return;
		}

		// Check if required ID is present based on POI type
		if (poi_identifier.poi_type === 'official' && !poi_identifier.poi_id) {
			setLoading(false);
			setReady(false);
			setError('POI ID is required for official POIs.');
			return;
		}
		if (
			poi_identifier.poi_type === 'user_temp' &&
			!poi_identifier.user_poi_temp_id
		) {
			setLoading(false);
			setReady(false);
			setError('User POI temp ID is required for temp POIs.');
			return;
		}
		if (
			poi_identifier.poi_type === 'user_approved' &&
			!poi_identifier.user_poi_approved_id
		) {
			setLoading(false);
			setReady(false);
			setError('User POI approved ID is required for approved POIs.');
			return;
		}

		// Only load visit details when auto_load is true (for individual loading strategy)
		if (auto_load) {
			setLoading(true);
			setReady(false);
			setError(null);
			const loadAll = async () => {
				try {
					// Load visits
					const response = await VisitsService.getPOIVisits(poi_identifier, {
						limit,
						offset: 0,
						sortBy: sort_by,
						sortOrder: sort_order,
					});
					if (response.success) {
						const visitInteractions = (response.interactions ||
							[]) as POIVisitInteraction[];
						setVisits(visitInteractions);
						setVisitCount(response.total_count || 0);
						setHasMore(response.has_more || false);
						setCurrentOffset(visitInteractions.length);
					} else {
						throw new Error(response.error || 'Failed to load visits');
					}
					// Load user visit
					if (currentUserId) {
						try {
							const result = await VisitsService.getUserVisitForPOI(
								poi_identifier,
								currentUserId
							);
							setUserVisit(result.visit);
							setHasVisited(result.hasVisited);
						} catch {
							setUserVisit(null);
							setHasVisited(false);
						}
					}
					if (!cancelled) setReady(true);
				} catch (err) {
					if (!cancelled) {
						const errorMessage =
							err instanceof Error ? err.message : 'Failed to load visits';
						setError(errorMessage);
						console.error('Error in useVisits loadAll:', err);
					}
				} finally {
					if (!cancelled) setLoading(false);
				}
			};
			loadAll();
		} else {
			// For batch loading strategy, just set ready to true without loading visit details
			setLoading(false);
			setReady(true);
		}

		return () => {
			cancelled = true;
		};
	}, [poi_identifier, currentUserId, limit, sort_by, sort_order, auto_load]);

	// Load visits for the POI
	const loadVisits = useCallback(
		async (resetOffset: boolean = false) => {
			if (!poi_identifier) return;

			setLoading(true);
			setError(null);

			const offsetToUse = resetOffset ? 0 : currentOffset;

			try {
				const response = await VisitsService.getPOIVisits(poi_identifier, {
					limit,
					offset: offsetToUse,
					sortBy: sort_by,
					sortOrder: sort_order,
				});

				if (response.success) {
					const visitInteractions = (response.interactions ||
						[]) as POIVisitInteraction[];

					if (resetOffset || offsetToUse === 0) {
						setVisits(visitInteractions);
					} else {
						setVisits((prev) => [...prev, ...visitInteractions]);
					}

					setVisitCount(response.total_count || 0);
					setHasMore(response.has_more || false);
					setCurrentOffset(offsetToUse + visitInteractions.length);
					console.log('Loaded visits:', {
						visitCount: response.total_count,
						visitsLength: visitInteractions.length,
					});
				} else {
					throw new Error(response.error || 'Failed to load visits');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load visits';
				setError(errorMessage);
				console.error('Error loading visits:', err);
			} finally {
				setLoading(false);
			}
		},
		[poi_identifier, currentOffset, limit, sort_by, sort_order]
	);

	// Load user's visit for this POI
	const loadUserVisit = useCallback(async () => {
		if (!poi_identifier || !currentUserId) {
			setUserVisit(null);
			setHasVisited(false);
			return;
		}

		try {
			const result = await VisitsService.getUserVisitForPOI(
				poi_identifier,
				currentUserId
			);
			setUserVisit(result.visit);
			setHasVisited(result.hasVisited);
			console.log('Loaded user visit state:', {
				hasVisited: result.hasVisited,
				visit: result.visit,
			});
		} catch {
			setUserVisit(null);
			setHasVisited(false);
		}
	}, [poi_identifier, currentUserId]);

	// Add a visit
	const addVisit = useCallback(async () => {
		if (!poi_identifier || !currentUserId) {
			setError('POI identifier and user ID are required');
			return;
		}
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping');
			return;
		}
		setActionLoading(true);
		setError(null);
		if (enable_optimistic_updates) {
			setHasVisited(true);
			setVisitCount((prev) => prev + 1);
		}
		try {
			const response = await VisitsService.addVisit(poi_identifier);
			if (response.success) {
				if (!enable_optimistic_updates) {
					setHasVisited(true);
					setVisitCount((prev) => prev + 1);
				}
			} else {
				throw new Error(response.error || 'Failed to add visit');
			}
		} catch (error) {
			if (enable_optimistic_updates) {
				setHasVisited(false);
				setVisitCount((prev) => Math.max(0, prev - 1));
			}
			setError(error instanceof Error ? error.message : 'Failed to add visit');
			console.error('Error adding visit:', error);
		} finally {
			setActionLoading(false);
		}
	}, [
		poi_identifier,
		currentUserId,
		ready,
		actionLoading,
		enable_optimistic_updates,
		loadVisitState,
	]);

	// Remove a visit
	const removeVisit = useCallback(async () => {
		if (!poi_identifier || !currentUserId) {
			setError('POI identifier and user ID are required');
			return;
		}
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping');
			return;
		}
		setActionLoading(true);
		setError(null);
		if (enable_optimistic_updates) {
			setHasVisited(false);
			setVisitCount((prev) => Math.max(0, prev - 1));
		}
		try {
			const response = await VisitsService.removeVisit(poi_identifier);
			if (response.success) {
				if (!enable_optimistic_updates) {
					setHasVisited(false);
					setVisitCount((prev) => Math.max(0, prev - 1));
				}
			} else {
				throw new Error(response.error || 'Failed to remove visit');
			}
		} catch (err) {
			if (enable_optimistic_updates) {
				setHasVisited(true);
				setVisitCount((prev) => prev + 1);
			}
			setError(err instanceof Error ? err.message : 'Failed to remove visit');
			console.error('Error removing visit:', err);
		} finally {
			setActionLoading(false);
		}
	}, [
		poi_identifier,
		currentUserId,
		ready,
		actionLoading,
		enable_optimistic_updates,
		visitCount,
		loadVisitState,
	]);

	// Toggle visit status
	const toggleVisit = useCallback(async () => {
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping toggle');
			return;
		}
		if (hasVisited) {
			await removeVisit();
		} else {
			await addVisit();
		}
	}, [ready, actionLoading, hasVisited, addVisit, removeVisit]);

	// Refresh all data
	const refresh = useCallback(async () => {
		setCurrentOffset(0);
		await Promise.all([loadVisits(true), loadUserVisit()]);
	}, [loadVisits, loadUserVisit]);

	// Auto-load on mount and when dependencies change (simplified like likes)
	useEffect(() => {
		if (!poi_identifier) {
			setError('POI identifier is missing. Cannot load visits.');
			setReady(false);
			console.warn('useVisits: POI identifier is missing.');
			return;
		}

		// Validate POI identifier
		if (
			!poi_identifier.poi_type ||
			!['official', 'user_temp', 'user_approved'].includes(
				poi_identifier.poi_type
			)
		) {
			setError('Invalid POI type. Cannot load visits.');
			setReady(false);
			console.warn('useVisits: Invalid POI type:', poi_identifier.poi_type);
			return;
		}

		// Check if required ID is present based on POI type
		if (poi_identifier.poi_type === 'official' && !poi_identifier.poi_id) {
			setError('POI ID is required for official POIs. Cannot load visits.');
			setReady(false);
			console.warn('useVisits: POI ID missing for official POI');
			return;
		}
		if (
			poi_identifier.poi_type === 'user_temp' &&
			!poi_identifier.user_poi_temp_id
		) {
			setError(
				'User POI temp ID is required for temp POIs. Cannot load visits.'
			);
			setReady(false);
			console.warn('useVisits: User POI temp ID missing for temp POI');
			return;
		}
		if (
			poi_identifier.poi_type === 'user_approved' &&
			!poi_identifier.user_poi_approved_id
		) {
			setError(
				'User POI approved ID is required for approved POIs. Cannot load visits.'
			);
			setReady(false);
			console.warn('useVisits: User POI approved ID missing for approved POI');
			return;
		}
		if (auto_load) {
			const timeoutId = setTimeout(() => {
				const loadData = async () => {
					setLoading(true);
					setError(null);
					try {
						if (currentUserId) {
							// Load user-specific visit status
							const response = await VisitsService.getUserVisitStatus(
								poi_identifier,
								currentUserId
							);
							if (response.success) {
								setHasVisited(response.hasVisited);
								setVisitCount(response.visitCount || 0);
								console.log('Loaded visit state:', {
									hasVisited: response.hasVisited,
									visitCount: response.visitCount,
								});
							} else {
								throw new Error('Failed to load visit status');
							}
						} else {
							// Load public visit count only (no user authentication)
							const response = await VisitsService.getPOIVisitCount(
								poi_identifier
							);
							if (response.success) {
								setHasVisited(false);
								setVisitCount(response.visitCount || 0);
								console.log('Loaded public visit count:', response.visitCount);
							} else {
								throw new Error('Failed to load visit count');
							}
						}
					} catch (err) {
						const errorMessage =
							err instanceof Error ? err.message : 'Failed to load visit data';
						setError(errorMessage);
						console.error('Error loading visit state:', err);
					} finally {
						setLoading(false);
						setReady(true);
						console.log('useVisits: ready set to true');
					}
				};
				loadData();
			}, 100);
			return () => clearTimeout(timeoutId);
		}
	}, [auto_load, poi_identifier, currentUserId]);

	// Load more visits
	const loadMore = useCallback(async () => {
		if (!hasMore || loading) return;
		await loadVisits(false);
	}, [hasMore, loading, loadVisits]);

	return {
		visits,
		userVisit,
		visitCount,
		hasVisited,
		loading,
		actionLoading,
		ready,
		error,
		hasMore,
		addVisit,
		removeVisit,
		toggleVisit,
		loadVisits,
		loadMore,
		refresh,
		loadUserVisit,
		loadVisitState,
	};
};

// Hook for managing user's visits across all POIs
export const useUserVisits = ({
	user_id,
	auto_load = true,
	limit = 20,
	offset = 0,
}: UseUserVisitsOptions = {}): UseUserVisitsResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [visits, setVisits] = useState<POIVisitInteraction[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(false);
	const [totalCount, setTotalCount] = useState(0);
	const [currentOffset, setCurrentOffset] = useState(offset);

	// Load user's visits
	const loadVisits = useCallback(
		async (
			userId?: string,
			resetOffset: boolean = false,
			customOffset?: number
		) => {
			const userIdToUse = userId || currentUserId;
			if (!userIdToUse) return;

			setLoading(true);
			setError(null);

			const offsetToUse =
				customOffset !== undefined
					? customOffset
					: resetOffset
					? 0
					: currentOffset;

			try {
				const response = await VisitsService.getUserVisits(
					userIdToUse,
					limit,
					offsetToUse
				);

				if (response.success) {
					// Cast to POIVisitInteraction since we know these are visits
					const visitInteractions = (response.interactions ||
						[]) as POIVisitInteraction[];

					if (resetOffset || offsetToUse === 0) {
						setVisits(visitInteractions);
					} else {
						setVisits((prev) => [...prev, ...visitInteractions]);
					}

					setTotalCount(response.total_count || 0);
					setHasMore(response.has_more || false);
					setCurrentOffset(offsetToUse + visitInteractions.length);
				} else {
					throw new Error(response.error || 'Failed to load visits');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load visits';
				setError(errorMessage);
				console.error('Error loading visits:', err);
			} finally {
				setLoading(false);
			}
		},
		[currentUserId, limit]
	);

	// Load more visits
	const loadMore = useCallback(async () => {
		if (!hasMore || loading) return;
		await loadVisits(undefined, false, currentOffset);
	}, [hasMore, loading, loadVisits, currentOffset]);

	// Refresh visits
	const refresh = useCallback(async () => {
		setCurrentOffset(0);
		await loadVisits(undefined, true, 0);
	}, [loadVisits]);

	// Remove a visit
	const removeVisit = useCallback(
		async (visitId: string | number) => {
			try {
				// Find the visit to remove
				const visitToRemove = visits.find((visit) => visit.id === visitId);
				if (!visitToRemove) {
					throw new Error('Visit not found');
				}

				// Convert to POI identifier
				const poiIdentifier = {
					poi_type: visitToRemove.poi_type,
					poi_id: visitToRemove.poi_id,
					user_poi_temp_id: visitToRemove.user_poi_temp_id,
					user_poi_approved_id: visitToRemove.user_poi_approved_id,
				};

				const response = await VisitsService.removeVisitById(
					poiIdentifier,
					visitId
				);

				if (response.success) {
					// Remove from local state
					setVisits((prev) => prev.filter((visit) => visit.id !== visitId));
					setTotalCount((prev) => Math.max(0, prev - 1));
				} else {
					throw new Error(response.error || 'Failed to remove visit');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to remove visit';
				setError(errorMessage);
				console.error('Error removing visit:', err);
				throw err;
			}
		},
		[visits]
	);

	// Auto-load on mount
	useEffect(() => {
		if (auto_load && currentUserId) {
			loadVisits(currentUserId, true);
		}
	}, [auto_load, currentUserId]);

	return {
		visits,
		loading,
		error,
		hasMore,
		totalCount,
		loadVisits: () => loadVisits(undefined, true),
		loadMore,
		refresh,
		removeVisit,
	};
};
