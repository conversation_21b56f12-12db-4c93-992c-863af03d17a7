/** @format */

// Like-specific service operations

import {
	LikeRequest,
	LikeResponse,
} from '@/app/shared/userInteractions/likes/types';
import { BaseInteractionService } from '@/app/shared/userInteractions/shared/services';
import {
	InteractionListResponse,
	POIIdentifier,
} from '@/app/shared/userInteractions/shared/types';

export class LikesService extends BaseInteractionService {
	// Add a like to a POI
	static async addLike(poi: POIIdentifier): Promise<LikeResponse> {
		return this.updateInteraction(poi, 'like', 'add');
	}

	// Remove a like from a POI
	static async removeLike(poi: POIIdentifier): Promise<LikeResponse> {
		return this.updateInteraction(poi, 'like', 'remove');
	}

	// Toggle like status
	static async toggleLike(
		poi: POIIdentifier,
		currentlyLiked: boolean
	): Promise<LikeResponse> {
		return currentlyLiked ? this.removeLike(poi) : this.addLike(poi);
	}

	// Get user's like status for a POI
	static async getUserLikeStatus(
		poi: POIIdentifier,
		userId: string
	): Promise<{
		success: boolean;
		isLiked: boolean;
		likeCount: number;
	}> {
		try {
			// Get user's like status
			const userResponse = await this.getUserInteractions(poi, userId, 'like');

			if (!userResponse.success) {
				// If it's an authentication error, return default values instead of throwing
				if (
					userResponse.error?.includes('Authentication required') ||
					userResponse.error?.includes('Access denied')
				) {
					// Still try to get the public like count
					const countResponse = await this.getPOILikeCount(poi);
					const likeCount = countResponse.success ? countResponse.likeCount : 0;

					return {
						success: true,
						isLiked: false,
						likeCount,
					};
				}
				throw new Error(userResponse.error || 'Failed to get like status');
			}

			// Check if user has liked this POI
			const userLike = userResponse.interactions?.find(
				(interaction) =>
					interaction.user_id === userId &&
					interaction.interaction_type === 'like'
			);
			const isLiked = !!userLike;

			// Get total like count for the POI
			const countResponse = await this.getPOILikeCount(poi);
			const likeCount = countResponse.success ? countResponse.likeCount : 0;

			return {
				success: true,
				isLiked,
				likeCount,
			};
		} catch (error) {
			// Handle HTTP errors gracefully
			const errorMessage = super.handleApiError(error);
			if (
				errorMessage.includes('401') ||
				errorMessage.includes('403') ||
				errorMessage.includes('Authentication required')
			) {
				// Still try to get the public like count
				try {
					const countResponse = await this.getPOILikeCount(poi);
					const likeCount = countResponse.success ? countResponse.likeCount : 0;

					return {
						success: true,
						isLiked: false,
						likeCount,
					};
				} catch {
					return {
						success: true,
						isLiked: false,
						likeCount: 0,
					};
				}
			}
			throw new Error(errorMessage);
		}
	}

	// Get all users who liked a POI
	static async getPOILikes(
		poi: POIIdentifier,
		limit: number = 20,
		offset: number = 0
	): Promise<InteractionListResponse> {
		const params = {
			...this.buildPOIParams(poi),
			interactionType: 'like',
			limit,
			offset,
		};

		try {
			const response = await this.get<InteractionListResponse>(
				'/interactions',
				params
			);

			// The response already has the correct format for InteractionListResponse
			return {
				success: response.success,
				interactions: response.interactions || [],
				total_count: response.total_count,
				has_more: response.has_more,
				error: response.error,
			};
		} catch (error) {
			return {
				success: false,
				interactions: [],
				total_count: 0,
				has_more: false,
				error: super.handleApiError(error),
			};
		}
	}

	// Get public like count for a POI (no authentication required)
	static async getPOILikeCount(poi: POIIdentifier): Promise<{
		success: boolean;
		likeCount: number;
	}> {
		try {
			const response = await this.getPOILikes(poi, 1, 0); // Just get count, not actual data

			if (!response.success) {
				throw new Error(response.error || 'Failed to get like count');
			}

			return {
				success: true,
				likeCount: response.total_count || 0,
			};
		} catch (error) {
			throw new Error(super.handleApiError(error));
		}
	}

	// Get user's liked POIs
	static async getUserLikes(
		userId: string,
		limit: number = 20,
		offset: number = 0
	): Promise<InteractionListResponse> {
		const params = {
			userId,
			interactionType: 'like',
			limit,
			offset,
		};

		return this.get<InteractionListResponse>('/interactions', params);
	}

	// Batch like operations
	static async batchToggleLikes(
		requests: Array<{ poi: POIIdentifier; currentlyLiked: boolean }>
	): Promise<
		Array<{
			poi: POIIdentifier;
			success: boolean;
			error?: string;
			newCount?: number;
		}>
	> {
		const results = [];

		for (const request of requests) {
			try {
				const response = await this.toggleLike(
					request.poi,
					request.currentlyLiked
				);
				results.push({
					poi: request.poi,
					success: response.success,
					newCount: response.like_count,
				});
			} catch (error) {
				results.push({
					poi: request.poi,
					success: false,
					error: super.handleApiError(error),
				});
			}
		}

		return results;
	}

	// Utility methods
	static createLikeRequest(
		poi: POIIdentifier,
		action: 'add' | 'remove'
	): LikeRequest {
		return {
			poi_identifier: poi,
			action,
		};
	}

	static validateLikeResponse(response: unknown): response is LikeResponse {
		return (
			typeof response === 'object' &&
			response !== null &&
			'success' in response &&
			typeof (response as { success: unknown }).success === 'boolean'
		);
	}
}
