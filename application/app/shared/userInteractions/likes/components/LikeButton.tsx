/** @format */

'use client';

import { useLikes } from '@/app/shared/userInteractions/likes/hooks';
import {
	POIIdentifier,
	POIType,
} from '@/app/shared/userInteractions/shared/types';
import { useSession } from 'next-auth/react';
import React from 'react';
import { FaHeart, FaRegHeart } from 'react-icons/fa';

interface LikeButtonProps {
	// POI identification
	poiId?: number | null;
	poiType: POIType;
	userPoiTempId?: number | null;
	userPoiApprovedId?: number | null;

	// Display options
	showCount?: boolean;
	showLabel?: boolean;
	size?: 'sm' | 'md' | 'lg';
	variant?: 'minimal' | 'outlined' | 'filled';

	// Interaction options
	enableOptimisticUpdates?: boolean;
	autoLoad?: boolean;

	// Callbacks
	onLikeChange?: (isLiked: boolean, count: number) => void;
	onError?: (error: string) => void;
}

export const LikeButton: React.FC<LikeButtonProps> = ({
	poiId,
	poiType,
	userPoiTempId,
	userPoiApprovedId,
	showCount = true,
	showLabel = false,
	size = 'md',
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	variant = 'minimal',
	enableOptimisticUpdates = true,
	autoLoad = true,
	onLikeChange,
	onError,
}) => {
	const { data: session } = useSession();

	// Create POI identifier
	const poiIdentifier: POIIdentifier = {
		poi_id: poiId,
		user_poi_temp_id: userPoiTempId,
		user_poi_approved_id: userPoiApprovedId,
		poi_type: poiType,
	};

	// Use the likes hook
	const { likeCount, isLiked, loading, error, toggleLike } = useLikes({
		poi_identifier: poiIdentifier,
		user_id: session?.user?.id,
		auto_load: autoLoad,
		enable_optimistic_updates: enableOptimisticUpdates,
	});

	// Handle errors
	React.useEffect(() => {
		if (error && onError) {
			onError(error);
		}
	}, [error, onError]);

	// Handle like changes
	React.useEffect(() => {
		if (onLikeChange) {
			onLikeChange(isLiked, likeCount);
		}
	}, [isLiked, likeCount, onLikeChange]);

	// Handle click
	const handleClick = async () => {
		if (!session?.user?.id) {
			alert('Please sign in to like POIs');
			return;
		}

		try {
			await toggleLike();
		} catch (err) {
			console.error('Error toggling like:', err);
		}
	};

	// Utility functions
	const formatCount = (count: number): string => {
		if (count < 1000) return count.toString();
		if (count < 1000000) return `${(count / 1000).toFixed(1)}k`;
		return `${(count / 1000000).toFixed(1)}m`;
	};

	const getIconSize = () => {
		switch (size) {
			case 'sm':
				return 'w-3 h-3';
			case 'lg':
				return 'w-5 h-5';
			default:
				return 'w-4 h-4';
		}
	};

	const getButtonClass = () => {
		const baseClass =
			'flex flex-col items-center gap-1 p-2 transition-colors duration-200';

		const loadingClass = loading ? 'opacity-75 cursor-wait' : 'cursor-pointer';
		return `${baseClass} ${loadingClass}`;
	};

	const getIconColor = () => {
		return isLiked ? 'text-red-500' : 'text-gray-400 hover:text-red-400';
	};

	return (
		<button
			onClick={handleClick}
			disabled={loading}
			className={getButtonClass()}
			title={`${isLiked ? 'Unlike' : 'Like'} this place`}>
			{/* Icon - centered and colored */}
			<span
				className={`${getIconSize()} ${getIconColor()} transition-colors duration-200 flex items-center justify-center`}>
				{loading ? (
					<div className='animate-spin rounded-full border-2 border-current border-t-transparent' />
				) : isLiked ? (
					<FaHeart />
				) : (
					<FaRegHeart />
				)}
			</span>

			{/* Count underneath - simple text */}
			{showCount && (
				<span className='text-sm font-medium text-gray-600'>
					{formatCount(likeCount)}
				</span>
			)}

			{/* Label if enabled */}
			{showLabel && (
				<span className='text-xs text-gray-500'>
					{isLiked ? 'Liked' : 'Like'}
				</span>
			)}
		</button>
	);
};

export default LikeButton;
