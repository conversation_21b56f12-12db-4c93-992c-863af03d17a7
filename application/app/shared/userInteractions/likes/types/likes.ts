/** @format */

// Like-specific types and interfaces

import {
	BaseInteraction,
	BaseInteractionResponse,
	POIIdentifier,
} from '@/app/shared/userInteractions/shared/types';

export interface LikeInteraction extends BaseInteraction {
	interaction_type: 'like';
}

export interface POILikeInteraction extends LikeInteraction, POIIdentifier {}

export interface LikeRequest {
	poi_identifier: POIIdentifier;
	action: 'add' | 'remove';
}

export interface LikeResponse extends BaseInteractionResponse {
	like?: POILikeInteraction;
	like_count?: number;
	is_liked?: boolean;
}

export interface UseLikesOptions {
	poi_identifier?: POIIdentifier;
	user_id?: string;
	auto_load?: boolean;
	enable_optimistic_updates?: boolean;
}

export interface UseLikesResult {
	// State
	likeCount: number;
	isLiked: boolean;
	loading: boolean;
	actionLoading: boolean; // true only during user action
	ready: boolean; // true when initial state is loaded and ready for actions
	error: string | null;
	// initialStateLoaded: boolean // deprecated

	// Actions
	toggleLike: () => Promise<void>;
	addLike: () => Promise<void>;
	removeLike: () => Promise<void>;

	// Data loading
	refresh: () => Promise<void>;
	loadLikeState: () => Promise<void>;
}

// User likes across all POIs
export interface UseUserLikesOptions {
	user_id?: string;
	auto_load?: boolean;
	limit?: number;
	offset?: number;
}

export interface UseUserLikesResult {
	// State
	likes: POILikeInteraction[];
	loading: boolean;
	error: string | null;
	hasMore: boolean;
	totalCount: number;

	// Actions
	loadLikes: (userId?: string) => Promise<void>;
	loadMore: () => Promise<void>;
	refresh: () => Promise<void>;
	removeLike: (likeId: string | number) => Promise<void>;
}

// Utility functions for likes
export const createLikeRequest = (
	poi: POIIdentifier,
	action: 'add' | 'remove'
): LikeRequest => ({
	poi_identifier: poi,
	action,
});

export const isValidLikeResponse = (
	response: unknown
): response is LikeResponse => {
	return (
		typeof response === 'object' &&
		response !== null &&
		'success' in response &&
		typeof (response as { success: unknown }).success === 'boolean'
	);
};
