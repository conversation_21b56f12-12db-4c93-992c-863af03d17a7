'use client'

import React from 'react'
import { FaHeart, FaBookmark, FaMapMarkerAlt, FaEye, FaShare, FaStar } from 'react-icons/fa'
import { useInteractions } from '@/app/shared/userInteractions/hooks'
import { POIIdentifier, POIType, InteractionCounts } from '@/app/shared/userInteractions/shared/types'

interface InteractionStatsProps {
  // POI identification
  poiId?: number | null
  poiType: POIType
  userPoiTempId?: number | null
  userPoiApprovedId?: number | null

  // Initial counts (optional - will be overridden by hook data)
  initialCounts?: Partial<InteractionCounts>

  // Display options
  showIcons?: boolean
  showLabels?: boolean
  layout?: 'horizontal' | 'vertical' | 'grid'
  size?: 'sm' | 'md' | 'lg'
  variant?: 'minimal' | 'card' | 'detailed'
  
  // Specific stats to show
  showLikes?: boolean
  showSaves?: boolean
  showVisits?: boolean
  showViews?: boolean
  showShares?: boolean
  showReviews?: boolean
  
  // Interaction options
  autoLoad?: boolean
  
  // Callbacks
  onStatsLoad?: (counts: InteractionCounts) => void
  onError?: (error: string) => void
}

export const InteractionStats: React.FC<InteractionStatsProps> = ({
  poiId,
  poiType,
  userPoiTempId,
  userPoiApprovedId,
  initialCounts = {},
  showIcons = true,
  showLabels = false,
  layout = 'horizontal',
  size = 'md',
  variant = 'minimal',
  showLikes = true,
  showSaves = true,
  showVisits = true,
  showViews = false,
  showShares = false,
  showReviews = true,
  autoLoad = true,
  onStatsLoad,
  onError
}) => {
  // Create POI identifier
  const poiIdentifier: POIIdentifier = {
    poi_id: poiId,
    user_poi_temp_id: userPoiTempId,
    user_poi_approved_id: userPoiApprovedId,
    poi_type: poiType
  }

  // Use the master interaction hook
  const {
    counts,
    loading,
    error
  } = useInteractions({
    poi_identifier: poiIdentifier,
    auto_load: autoLoad,
    enable_optimistic_updates: false // Stats don't need optimistic updates
  })

  // Handle stats load
  React.useEffect(() => {
    if (onStatsLoad && !loading && !error) {
      onStatsLoad(counts)
    }
  }, [counts, loading, error, onStatsLoad])

  // Handle errors
  React.useEffect(() => {
    if (error && onError) {
      onError(error)
    }
  }, [error, onError])

  // Use initial counts if data is still loading
  const displayCounts = loading ? { ...counts, ...initialCounts } : counts

  // Utility functions
  const formatCount = (count: number): string => {
    if (count < 1000) return count.toString()
    if (count < 1000000) return `${(count / 1000).toFixed(1)}k`
    return `${(count / 1000000).toFixed(1)}m`
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 'w-3 h-3'
      case 'lg': return 'w-5 h-5'
      default: return 'w-4 h-4'
    }
  }

  const getTextSize = () => {
    switch (size) {
      case 'sm': return 'text-xs'
      case 'lg': return 'text-base'
      default: return 'text-sm'
    }
  }

  const getStatClass = () => {
    const baseClass = 'flex items-center gap-1'
    
    if (variant === 'minimal') {
      return `${baseClass} text-gray-600`
    }
    
    if (variant === 'card') {
      return `${baseClass} px-2 py-1 bg-gray-100 rounded text-gray-700`
    }
    
    // Detailed variant
    return `${baseClass} px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-gray-700`
  }

  const renderStat = (
    icon: React.ReactNode,
    count: number,
    label: string,
    show: boolean,
    color: string = 'text-gray-600'
  ) => {
    if (!show) return null

    return (
      <div className={getStatClass()}>
        {showIcons && (
          <span className={`${getIconSize()} ${color}`}>
            {icon}
          </span>
        )}
        <span className={`${getTextSize()} font-medium`}>
          {formatCount(count)}
        </span>
        {showLabels && (
          <span className={`${getTextSize()} text-gray-500`}>
            {label}
          </span>
        )}
      </div>
    )
  }

  const getContainerClass = () => {
    const baseClass = 'flex'
    const gapClass = variant === 'detailed' ? 'gap-3' : 'gap-2'
    
    if (layout === 'vertical') {
      return `${baseClass} flex-col ${gapClass}`
    }
    
    if (layout === 'grid') {
      return `grid grid-cols-2 ${gapClass}`
    }
    
    // Horizontal layout
    return `${baseClass} flex-row flex-wrap ${gapClass}`
  }

  if (loading && Object.keys(initialCounts).length === 0) {
    return (
      <div className="flex items-center gap-2 text-gray-400">
        <div className="animate-pulse flex space-x-2">
          <div className="h-4 w-8 bg-gray-200 rounded"></div>
          <div className="h-4 w-8 bg-gray-200 rounded"></div>
          <div className="h-4 w-8 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={getContainerClass()}>
      {/* Likes */}
      {renderStat(
        <FaHeart />,
        displayCounts.like_count,
        'Likes',
        showLikes,
        'text-red-500'
      )}

      {/* Favorites */}
      {renderStat(
        <FaBookmark />,
        displayCounts.favorite_count,
        'Favorites',
        showSaves,
        'text-blue-500'
      )}

      {/* Visits */}
      {renderStat(
        <FaMapMarkerAlt />,
        displayCounts.visit_count,
        'Visits',
        showVisits,
        'text-green-500'
      )}

      {/* Visits */}
      {renderStat(
        <FaEye />,
        displayCounts.visit_count,
        'Visits',
        showViews,
        'text-gray-500'
      )}

      {/* Shares */}
      {renderStat(
        <FaShare />,
        displayCounts.share_count,
        'Shares',
        showShares,
        'text-purple-500'
      )}

      {/* Reviews */}
      {renderStat(
        <FaStar />,
        displayCounts.review_count,
        'Reviews',
        showReviews,
        'text-yellow-500'
      )}
    </div>
  )
}

export default InteractionStats
