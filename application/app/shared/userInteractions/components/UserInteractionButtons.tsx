/** @format */

'use client';

import { useInteractions } from '@/app/shared/userInteractions/hooks';
import {
	POIIdentifier,
	POIType,
} from '@/app/shared/userInteractions/shared/types/base';
import { useSession } from 'next-auth/react';
import React, { useEffect, useMemo } from 'react';
import {
	FaEdit,
	FaHeart,
	FaMapMarkerAlt,
	FaRegHeart,
	FaRegStar,
	FaStar,
} from 'react-icons/fa';

interface UserInteractionButtonsProps {
	poiId?: number | null;
	poiType: POIType;
	userPoiTempId?: number | null;
	userPoiApprovedId?: number | null;

	// Pre-loaded interaction data (optional)
	interactionData?: {
		like_count: number;
		favorite_count: number;
		visit_count: number;
		user_has_liked: boolean;
		user_has_favorited: boolean;
		user_has_visited: boolean;
	};

	// Callbacks
	onWriteReview?: () => void;

	// Layout options
	layout?: 'horizontal' | 'vertical' | 'compact';
	showCounts?: boolean;
	showLabels?: boolean;
	size?: 'sm' | 'md' | 'lg';

	// New options
	enableOptimisticUpdates?: boolean;
	autoLoad?: boolean;
	loadingStrategy?: 'batch' | 'individual' | 'none';
}

export default function UserInteractionButtons({
	poiId,
	poiType,
	userPoiTempId,
	userPoiApprovedId,
	interactionData,
	onWriteReview,
	layout = 'horizontal',
	showCounts = true,
	showLabels = false,
	size = 'md',
	enableOptimisticUpdates = true,
	autoLoad = true,
	loadingStrategy = 'batch',
}: UserInteractionButtonsProps) {
	const { data: session } = useSession();

	// Create POI identifier
	const poiIdentifier: POIIdentifier = useMemo(
		() => ({
			poi_id: poiId,
			user_poi_temp_id: userPoiTempId,
			user_poi_approved_id: userPoiApprovedId,
			poi_type: poiType,
		}),
		[poiId, userPoiTempId, userPoiApprovedId, poiType]
	);

	// Use the new master interaction hook
	const {
		counts,
		states,
		loadingStates,
		readyStates,
		error,
		likes,
		visits,
		favorites,
	} = useInteractions({
		poi_identifier: poiIdentifier,
		auto_load: autoLoad,
		enable_optimistic_updates: enableOptimisticUpdates,
		loading_strategy: loadingStrategy,
		initialData: interactionData,
	});

	// Note: We don't automatically notify parent of count changes to avoid infinite loops
	// The parent should get updated counts from the hooks directly

	// Log errors and auto-clear after 5 seconds
	useEffect(() => {
		if (error) {
			console.error('Interaction error:', error);
			const timer = setTimeout(() => {
				// The error will be cleared when the next interaction succeeds
				// or when the component unmounts
			}, 5000);
			return () => clearTimeout(timer);
		}
	}, [error]);

	// Handle like action
	const handleLike = async () => {
		if (!session?.user?.id) {
			alert('Please sign in to interact with POIs');
			return;
		}
		if (!readyStates.likes) {
			console.log('Like state not loaded yet, skipping');
			return;
		}
		if (loadingStates.likes) {
			console.log('Like action already in progress');
			return;
		}
		try {
			await likes.toggle();
		} catch (error) {
			console.error('Error toggling like:', error);
		}
	};

	// Handle favorite action
	const handleFavorite = async () => {
		if (!session?.user?.id) {
			alert('Please sign in to favorite POIs');
			return;
		}
		if (!readyStates.favorites) {
			console.log('Favorite state not loaded yet, skipping');
			return;
		}
		if (loadingStates.favorites) {
			console.log('Favorite action already in progress');
			return;
		}
		try {
			await favorites.toggle();
		} catch (error) {
			console.error('Error toggling favorite:', error);
		}
	};

	// Handle visit action
	const handleVisit = async () => {
		if (!session?.user?.id) {
			alert('Please sign in to interact with visits');
			return;
		}
		if (!readyStates.visits) {
			console.log('Visit state not loaded yet, skipping');
			return;
		}
		if (loadingStates.visits) {
			console.log('Visit action already in progress');
			return;
		}
		try {
			await visits.toggle();
			// console.log('Visit toggled successfully')
		} catch (error) {
			console.error('Error toggling visit:', error);
		}
	};

	// Error is now logged in useEffect above to prevent render loops

	// Utility functions
	const formatCount = (count: number): string => {
		if (count < 1000) return count.toString();
		if (count < 1000000) return `${(count / 1000).toFixed(1)}k`;
		return `${(count / 1000000).toFixed(1)}m`;
	};

	const getIconSize = () => {
		switch (size) {
			case 'sm':
				return 'w-5 h-5';
			case 'lg':
				return 'w-8 h-8';
			default:
				return 'w-6 h-6';
		}
	};

	const renderButton = (
		icon: React.ReactNode,
		activeIcon: React.ReactNode,
		isActive: boolean,
		count: number,
		label: string,
		onClick: () => void,
		isLoading: boolean = false
	) => {
		// Simple button styling - no box, just icon with count underneath
		const buttonClass = `
      flex flex-col items-center gap-1 p-2 transition-colors duration-200
      ${isLoading ? 'opacity-75 cursor-wait' : 'cursor-pointer'}
    `;

		// Get icon colors based on interaction type and state
		const getIconColor = () => {
			if (label === 'Favorite') {
				return isActive
					? 'text-yellow-500'
					: 'text-gray-400 hover:text-yellow-400';
			} else if (label === 'Like this place') {
				return isActive ? 'text-red-500' : 'text-gray-400 hover:text-red-400';
			} else if (label === 'Mark as visited') {
				return isActive
					? 'text-green-500'
					: 'text-gray-400 hover:text-green-400';
			} else {
				return isActive ? 'text-blue-500' : 'text-gray-400 hover:text-blue-400';
			}
		};

		const iconColor = getIconColor();

		return (
			<button
				onClick={onClick}
				disabled={isLoading}
				className={buttonClass}
				title={label}>
				{/* Icon - centered and colored */}
				<div
					className={`${getIconSize()} ${iconColor} transition-colors duration-200 flex items-center justify-center`}>
					{isLoading ? (
						<div className='animate-spin rounded-full border-2 border-current border-t-transparent' />
					) : isActive ? (
						activeIcon
					) : (
						icon
					)}
				</div>

				{/* Count underneath - simple text */}
				{showCounts && (
					<span className='text-sm font-medium text-gray-600'>
						{formatCount(count)}
					</span>
				)}

				{/* Label if enabled */}
				{showLabels && (
					<span className='text-xs text-gray-500'>
						{label === 'Like this place'
							? 'Like'
							: label === 'Mark as visited'
							? 'Visited'
							: label}
					</span>
				)}
			</button>
		);
	};

	const containerClass = `
    flex items-center justify-center
    ${layout === 'vertical' ? 'flex-col gap-4' : 'flex-row gap-6'}
    ${layout === 'compact' ? 'gap-3' : 'gap-6'}
  `;

	return (
		<div className={containerClass}>
			{/* Error Display */}
			{error && (
				<div className='mb-2 p-2 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm'>
					{error}
				</div>
			)}

			{/* Like Button */}
			{renderButton(
				<FaRegHeart />,
				<FaHeart />,
				states.isLiked,
				counts.like_count,
				'Like this place',
				handleLike,
				loadingStates.likes || !readyStates.likes
			)}

			{/* Favorite Button */}
			{renderButton(
				<FaRegStar />, // Use outlined star for inactive
				<FaStar />, // Use filled star for active
				states.isFavorited,
				counts.favorite_count,
				'Favorite',
				handleFavorite,
				loadingStates.favorites || !readyStates.favorites
			)}

			{/* Visit Button */}
			{renderButton(
				<FaMapMarkerAlt />,
				<FaMapMarkerAlt />,
				states.hasVisited,
				counts.visit_count,
				'Mark as visited',
				handleVisit,
				loadingStates.visits || !readyStates.visits
			)}

			{/* Write Review Button */}
			{onWriteReview &&
				renderButton(
					<FaEdit />,
					<FaEdit />,
					false, // Reviews don't have an "active" state like likes/favorites
					counts.review_count || 0,
					'Write Review',
					onWriteReview,
					false // Reviews don't have loading state
				)}
		</div>
	);
}
