/** @format */

'use client';

import { useInteractions } from '@/app/shared/userInteractions/hooks';
import {
	POIIdentifier,
	POIType,
} from '@/app/shared/userInteractions/shared/types';
import { useSession } from 'next-auth/react';
import React from 'react';
import {
	FaBookmark,
	FaHeart,
	FaMapMarkerAlt,
	FaRegBookmark,
	FaRegHeart,
} from 'react-icons/fa';

interface InteractionCardProps {
	// POI identification
	poiId?: number | null;
	poiType: POIType;
	userPoiTempId?: number | null;
	userPoiApprovedId?: number | null;

	// Display options
	showLike?: boolean;
	showSave?: boolean;
	showVisit?: boolean;
	showCounts?: boolean;
	size?: 'sm' | 'md' | 'lg';
	variant?: 'minimal' | 'compact' | 'full';

	// Interaction options
	enableOptimisticUpdates?: boolean;
	autoLoad?: boolean;
	loadingStrategy?: 'batch' | 'individual' | 'none';

	// Callbacks
	onError?: (error: string) => void;
}

export const InteractionCard: React.FC<InteractionCardProps> = ({
	poiId,
	poiType,
	userPoiTempId,
	userPoiApprovedId,
	showLike = true,
	showSave = true,
	showVisit = true,
	showCounts = true,
	size = 'md',
	variant = 'compact',
	enableOptimisticUpdates = true,
	autoLoad = true,
	loadingStrategy = 'batch',
	onError,
}) => {
	const { data: session } = useSession();

	// Create POI identifier
	const poiIdentifier: POIIdentifier = {
		poi_id: poiId,
		user_poi_temp_id: userPoiTempId,
		user_poi_approved_id: userPoiApprovedId,
		poi_type: poiType,
	};

	// Use the master interaction hook
	const { counts, states, loading, error, likes, favorites, visits } =
		useInteractions({
			poi_identifier: poiIdentifier,
			auto_load: autoLoad,
			enable_optimistic_updates: enableOptimisticUpdates,
			loading_strategy: loadingStrategy,
		});

	// Handle errors
	React.useEffect(() => {
		if (error && onError) {
			onError(error);
		}
	}, [error, onError]);

	// Note: Removed interaction update callbacks to prevent infinite loops
	// Counts are managed by the hooks and updated automatically

	// Action handlers
	const handleLike = async () => {
		if (!session?.user?.id) {
			alert('Please sign in to like POIs');
			return;
		}

		try {
			await likes.toggle();
			// Count will be updated by the hook after refresh
		} catch (err) {
			console.error('Error toggling like:', err);
		}
	};

	const handleSave = async () => {
		if (!session?.user?.id) {
			alert('Please sign in to save POIs');
			return;
		}

		try {
			await favorites.toggle();
			// Count will be updated by the hook after refresh
		} catch (err) {
			console.error('Error toggling favorite:', err);
		}
	};

	const handleVisit = async () => {
		if (!session?.user?.id) {
			alert('Please sign in to mark as visited');
			return;
		}

		try {
			await visits.toggle();
			// Count will be updated by the hook after refresh
			console.log('Visit toggled successfully');
		} catch (err) {
			console.error('Error toggling visit:', err);
		}
	};

	// Utility functions
	const formatCount = (count: number): string => {
		if (count < 1000) return count.toString();
		if (count < 1000000) return `${(count / 1000).toFixed(1)}k`;
		return `${(count / 1000000).toFixed(1)}m`;
	};

	const getIconSize = () => {
		switch (size) {
			case 'sm':
				return 'w-3 h-3';
			case 'lg':
				return 'w-5 h-5';
			default:
				return 'w-4 h-4';
		}
	};

	const getButtonClass = (
		isActive: boolean,
		isLoading: boolean,
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		interactionType: 'like' | 'save' | 'visit'
	) => {
		const baseClass =
			'flex flex-col items-center gap-1 p-2 transition-colors duration-200';

		if (variant === 'minimal') {
			return `${baseClass} ${
				isActive ? 'text-blue-600' : 'text-gray-500 hover:text-gray-700'
			} ${isLoading ? 'opacity-50' : ''}`;
		}

		const loadingClass = isLoading
			? 'opacity-75 cursor-wait'
			: 'cursor-pointer';
		return `${baseClass} ${loadingClass}`;
	};

	const getIconColor = (
		interactionType: 'like' | 'save' | 'visit',
		isActive: boolean
	) => {
		if (interactionType === 'save') {
			return isActive
				? 'text-yellow-500'
				: 'text-gray-400 hover:text-yellow-400';
		} else if (interactionType === 'like') {
			return isActive ? 'text-red-500' : 'text-gray-400 hover:text-red-400';
		} else if (interactionType === 'visit') {
			return isActive ? 'text-green-500' : 'text-gray-400 hover:text-green-400';
		} else {
			return isActive ? 'text-blue-500' : 'text-gray-400 hover:text-blue-400';
		}
	};

	const renderInteractionButton = (
		icon: React.ReactNode,
		activeIcon: React.ReactNode,
		isActive: boolean,
		count: number,
		onClick: () => void,
		isLoading: boolean,
		show: boolean,
		interactionType: 'like' | 'save' | 'visit'
	) => {
		if (!show) return null;

		const iconColor = getIconColor(interactionType, isActive);

		return (
			<button
				onClick={onClick}
				disabled={isLoading}
				className={getButtonClass(isActive, isLoading, interactionType)}
				title={`${isActive ? 'Remove' : 'Add'} interaction`}>
				{/* Icon - centered and colored */}
				<span
					className={`${getIconSize()} ${iconColor} transition-colors duration-200 flex items-center justify-center`}>
					{isLoading ? (
						<div className='animate-spin rounded-full border-2 border-current border-t-transparent' />
					) : isActive ? (
						activeIcon
					) : (
						icon
					)}
				</span>

				{/* Count underneath - simple text */}
				{showCounts && variant !== 'minimal' && (
					<span className='text-sm font-medium text-gray-600'>
						{formatCount(count)}
					</span>
				)}
			</button>
		);
	};

	const containerClass = `flex items-center gap-2 ${
		variant === 'full' ? 'gap-3' : 'gap-2'
	}`;

	return (
		<div className={containerClass}>
			{/* Like Button */}
			{renderInteractionButton(
				<FaRegHeart />,
				<FaHeart />,
				states.isLiked,
				counts.like_count,
				handleLike,
				loading,
				showLike,
				'like'
			)}

			{/* Save Button */}
			{renderInteractionButton(
				<FaRegBookmark />,
				<FaBookmark />,
				states.isFavorited,
				counts.favorite_count,
				handleSave,
				loading,
				showSave,
				'save'
			)}

			{/* Visit Button */}
			{renderInteractionButton(
				<FaMapMarkerAlt />,
				<FaMapMarkerAlt />,
				states.hasVisited,
				counts.visit_count,
				handleVisit,
				loading,
				showVisit,
				'visit'
			)}
		</div>
	);
};

export default InteractionCard;
