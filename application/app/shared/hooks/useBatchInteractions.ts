/**
 * Custom hook for batch loading POI interactions with lazy loading
 * Loads interaction data for multiple POIs when they come into viewport
 *
 * @format
 */

'use client';

import { useCallback, useRef, useState } from 'react';

interface POI {
	poi_id: number;
	poi_type: string;
}

interface POIInteractionData {
	poi_id: number;
	poi_type: string;
	like_count: number;
	favorite_count: number;
	visit_count: number;
	media_count: number;
	user_has_liked: boolean;
	user_has_favorited: boolean;
	user_has_visited: boolean;
}

interface BatchInteractionsState {
	[key: string]: POIInteractionData; // key format: "poiType_poiId"
}

interface UseBatchInteractionsReturn {
	interactions: BatchInteractionsState;
	loadInteractions: (pois: POI[]) => Promise<void>;
	getInteractionData: (poi: POI) => POIInteractionData | null;
	refreshInteractions: () => Promise<void>;
	isLoading: boolean;
	error: string | null;
}

export const useBatchInteractions = (): UseBatchInteractionsReturn => {
	const [interactions, setInteractions] = useState<BatchInteractionsState>({});
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Track which POIs are currently being loaded to prevent duplicate requests
	const loadingPOIs = useRef<Set<string>>(new Set());

	// Track which POIs have already been loaded
	const loadedPOIs = useRef<Set<string>>(new Set());

	/**
	 * Generate a unique key for a POI
	 */
	const getPOIKey = useCallback((poi: POI): string => {
		return `${poi.poi_type}_${poi.poi_id}`;
	}, []);

	/**
	 * Load interactions for a batch of POIs
	 */
	const loadInteractions = useCallback(
		async (pois: POI[]) => {
			if (!pois || pois.length === 0) return;

			// Filter out POIs that are already loaded or currently loading
			const poisToLoad = pois.filter((poi) => {
				const key = getPOIKey(poi);
				return !loadedPOIs.current.has(key) && !loadingPOIs.current.has(key);
			});

			if (poisToLoad.length === 0) return;

			// Mark POIs as loading
			poisToLoad.forEach((poi) => {
				loadingPOIs.current.add(getPOIKey(poi));
			});

			setIsLoading(true);
			setError(null);

			try {
				console.log(
					`📡 Making batch API call for ${poisToLoad.length} POIs:`,
					poisToLoad.map((p) => p.poi_id)
				);

				const response = await fetch('/api/pois/interactions', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						batch: true,
						pois: poisToLoad,
					}),
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				const data = await response.json();

				if (data.success) {
					console.log(
						`✅ Successfully loaded interactions for ${data.interactions.length} POIs`
					);

					// Update interactions state - convert new API format to expected format
					setInteractions((prev) => {
						const newInteractions = { ...prev };

						data.interactions.forEach(
							(interaction: {
								poi_id: number;
								poi_type: string;
								like?: { count: number; hasInteraction: boolean };
								favorite?: { count: number; hasInteraction: boolean };
								visit?: { count: number; hasInteraction: boolean };
							}) => {
								const key = getPOIKey({
									poi_id: interaction.poi_id,
									poi_type: interaction.poi_type,
								});

								// Convert new API format to expected format
								const convertedInteraction: POIInteractionData = {
									poi_id: interaction.poi_id,
									poi_type: interaction.poi_type,
									like_count: interaction.like?.count || 0,
									favorite_count: interaction.favorite?.count || 0,
									visit_count: interaction.visit?.count || 0,
									media_count: 0, // Not provided by new API
									user_has_liked: interaction.like?.hasInteraction || false,
									user_has_favorited:
										interaction.favorite?.hasInteraction || false,
									user_has_visited: interaction.visit?.hasInteraction || false,
								};

								newInteractions[key] = convertedInteraction;
								loadedPOIs.current.add(key);
							}
						);

						return newInteractions;
					});
				} else {
					throw new Error(data.error || 'Failed to load interactions');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Unknown error';
				setError(errorMessage);
				console.error('Error loading batch interactions:', err);
			} finally {
				// Remove POIs from loading set
				poisToLoad.forEach((poi) => {
					loadingPOIs.current.delete(getPOIKey(poi));
				});
				setIsLoading(false);
			}
		},
		[getPOIKey]
	);

	/**
	 * Get interaction data for a specific POI
	 */
	const getInteractionData = useCallback(
		(poi: POI): POIInteractionData | null => {
			const key = getPOIKey(poi);
			const data = interactions[key] || null;

			return data;
		},
		[interactions, getPOIKey]
	);

	/**
	 * Refresh all loaded interactions by reloading them
	 */
	const refreshInteractions = useCallback(async () => {
		const loadedKeys = Array.from(loadedPOIs.current);
		if (loadedKeys.length === 0) return;

		// Convert keys back to POI objects
		const poisToRefresh = loadedKeys.map((key) => {
			const [poi_type, poi_id] = key.split('_');
			return {
				poi_id: parseInt(poi_id),
				poi_type,
			};
		});

		// Clear the loaded POIs set to force reload
		loadedPOIs.current.clear();

		// Reload the interactions
		await loadInteractions(poisToRefresh);
	}, [loadInteractions]);

	return {
		interactions,
		loadInteractions,
		getInteractionData,
		refreshInteractions,
		isLoading,
		error,
	};
};
