/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/pois/filter - Get filtered POIs with spatial bounds and advanced filters
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);

		// Get filter parameters
		const city = searchParams.get('city')?.split(',').filter(Boolean) || [];
		const district =
			searchParams.get('district')?.split(',').filter(Boolean) || [];
		const neighborhood =
			searchParams.get('neighborhood')?.split(',').filter(Boolean) || [];
		const country =
			searchParams.get('country')?.split(',').filter(Boolean) || [];
		const category =
			searchParams.get('category')?.split(',').filter(Boolean) || [];
		const subcategory =
			searchParams.get('subcategory')?.split(',').filter(Boolean) || [];
		const cuisine =
			searchParams.get('cuisine')?.split(',').filter(Boolean) || [];
		const name = searchParams.get('name') || '';
		const bounds = searchParams.get('bounds');
		const page = parseInt(searchParams.get('page') || '1');
		const limit = parseInt(searchParams.get('limit') || '30');
		const offset = (page - 1) * limit;

		// Get user session for favorites
		const session = await getServerSession(authOptions);
		const userId = session?.user?.id;

		logger.info('Filtering POIs', {
			city: city.length,
			district: district.length,
			neighborhood: neighborhood.length,
			country: country.length,
			category: category.length,
			subcategory: subcategory.length,
			cuisine: cuisine.length,
			name,
			bounds,
			limit,
			page,
		});

		// Build WHERE conditions
		const conditions: string[] = [];
		const params: (string[] | string | number | null)[] = [];
		let paramIndex = 1;

		// City filter
		if (city.length > 0) {
			conditions.push(`city = ANY($${paramIndex++})`);
			params.push(city);
		}

		// District filter
		if (district.length > 0) {
			conditions.push(`district = ANY($${paramIndex++})`);
			params.push(district);
		}

		// Neighborhood filter
		if (neighborhood.length > 0) {
			conditions.push(`neighborhood = ANY($${paramIndex++})`);
			params.push(neighborhood);
		}

		// Country filter
		if (country.length > 0) {
			conditions.push(`country = ANY($${paramIndex++})`);
			params.push(country);
		}

		// Category filter
		if (category.length > 0) {
			conditions.push(`category = ANY($${paramIndex++})`);
			params.push(category);
		}

		// Subcategory filter
		if (subcategory.length > 0) {
			conditions.push(`subcategory = ANY($${paramIndex++})`);
			params.push(subcategory);
		}

		// Cuisine filter
		if (cuisine.length > 0) {
			conditions.push(`cuisine = ANY($${paramIndex++})`);
			params.push(cuisine);
		}

		// Name search
		if (name) {
			conditions.push(`name ILIKE $${paramIndex++}`);
			params.push(`%${name}%`);
		}

		// Spatial bounds filter
		if (bounds) {
			const [minLat, minLng, maxLat, maxLng] = bounds.split(',').map(Number);
			conditions.push(`latitude BETWEEN $${paramIndex++} AND $${paramIndex++}`);
			conditions.push(
				`longitude BETWEEN $${paramIndex++} AND $${paramIndex++}`
			);
			params.push(minLat, maxLat, minLng, maxLng);
		}

		const whereClause =
			conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

		// Build the main query - use only spatial_schema.pois table
		const query = `
      SELECT
        'official' as poi_type,
        p.id as poi_id,
        null as temp_id,
        null as approved_id,
        p.name,
        p.name_en,
        p.name_tr,
        p.name_uk,
        p.name_de,
        p.name_ru,
        p.name_ar,
        p.category,
        p.subcategory,
        p.latitude,
        p.longitude,
        p.city,
        p.district,
        p.country,
        p.phone_number,
        p.opening_hours,
        CASE
          WHEN uf.poi_id IS NOT NULL THEN true
          ELSE false
        END as is_favorite,
        p.neighborhood
      FROM spatial_schema.pois p
      LEFT JOIN backend_schema.user_favorites uf ON 
        uf.poi_id = p.id AND uf.user_id = $${paramIndex++}
      ${whereClause}
      ORDER BY p.name ASC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

		// Count query for pagination
		const countQuery = `
      SELECT COUNT(*) as total
      FROM spatial_schema.pois p
      ${whereClause}
    `;

		// Add user ID and pagination params
		const queryParams = [...params, userId || null, limit, offset];
		const countParams = [...params];

		// Execute queries
		const [poisResult, countResult] = await Promise.all([
			db.query(query, queryParams),
			db.query(countQuery, countParams),
		]);

		const totalCount = parseInt(countResult.rows[0]?.total || '0');

		logger.info(
			`Retrieved ${poisResult.rows.length} filtered POIs (page ${page})`
		);

		return NextResponse.json({
			success: true,
			pois: poisResult.rows,
			totalCount,
			page,
			limit,
			totalPages: Math.ceil(totalCount / limit),
		});
	} catch (error) {
		logger.error('Error filtering POIs', { error });
		return NextResponse.json(
			{ success: false, error: 'Failed to filter POIs' },
			{ status: 500 }
		);
	}
}
