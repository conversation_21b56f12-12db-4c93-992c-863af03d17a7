/** @format */

import { db } from '@/lib/database';
import { logger } from '@/lib/logger';
import { authOptions } from '@/lib/nextauth-options';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const {
			locationType,
			locationName,
			center,
			zoom,
			offset = 0,
			limit = 20,
			category,
			city,
			country,
		} = await request.json();

		if (!locationType || !locationName || !center) {
			return NextResponse.json(
				{ success: false, error: 'Missing required parameters' },
				{ status: 400 }
			);
		}

		// Get user session for favorites
		await getServerSession(authOptions);

		// Calculate search radius based on location type and zoom
		let defaultLimit = 20; // Default limit

		if (locationType === 'country') {
			defaultLimit = 50;
		} else if (locationType === 'city') {
			defaultLimit = 30;
		}

		// Adjust based on zoom level
		if (zoom) {
			const zoomFactor = Math.max(0.1, Math.min(2.0, zoom / 200));
			defaultLimit = Math.floor(defaultLimit * zoomFactor);
		}

		// Use provided limit or default
		const finalLimit = limit || defaultLimit;

		// Use the top_locations function for rankings
		// Don't use adminLevel filtering for country-level requests as it causes issues
		let adminLevel: number | null = null;
		if (locationType === 'city') adminLevel = 6;
		else if (locationType === 'region') adminLevel = 8;
		// For country-level requests, use country parameter instead of adminLevel

		// Map category and subcategory with country support
		const topLocationsQuery = `
      SELECT * FROM spatial_schema.get_top_locations($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
    `;
		const topLocationsParams = [
			category || null, // p_category
			null, // p_subcategory
			city || null, // p_city
			null, // p_district
			null, // p_neighborhood
			country || null, // p_country (NEW)
			null, // p_min_rating
			null, // p_max_rating
			adminLevel, // p_admin_level
			null, // p_admin_name
			null, // p_min_lat
			null, // p_max_lat
			null, // p_min_lng
			null, // p_max_lng
			offset, // p_offset
			finalLimit, // p_limit
		];

		const result = await db.query(topLocationsQuery, topLocationsParams);

		// Add poi_type field to all POIs since they come from the official spatial_schema.pois table
		const poisWithType = result.rows.map((poi) => ({
			...poi,
			poi_type: 'official',
			poi_id: poi.id, // Ensure poi_id is set for consistency
		}));

		return NextResponse.json({
			success: true,
			pois: poisWithType,
			locationType,
			locationName,
			count: poisWithType.length,
			offset,
			limit: finalLimit,
			hasMore: poisWithType.length === finalLimit,
			lastPOIId:
				poisWithType.length > 0
					? poisWithType[poisWithType.length - 1].id
					: null,
		});
	} catch (error) {
		const errorMeta =
			error instanceof Error
				? { message: error.message, stack: error.stack }
				: { error };
		logger.error('Error fetching POI rankings', errorMeta);
		return NextResponse.json(
			{ success: false, error: 'Failed to fetch POI rankings' },
			{ status: 500 }
		);
	}
}
