/** @format */

'use client';

import { POI_CATEGORIES, POI_SUBCATEGORY_OPTIONS } from '@/app/shared/poi';
import { logger } from '@/lib/logger';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
	FaCheck,
	FaClock,
	FaInfoCircle,
	FaMapMarkerAlt,
	FaPhone,
	FaSpinner,
	FaTimes,
} from 'react-icons/fa';

interface POISubmissionForm {
	name: string;
	name_en: string;
	name_tr: string;
	name_uk: string;
	name_de: string;
	name_ru: string;
	name_ar: string;
	category: string;
	subcategory: string;
	cuisine: string;
	latitude: number | null;
	longitude: number | null;
	city: string;
	district: string;
	neighborhood: string;
	street: string;
	full_address: string;
	province: string;
	phone_number: string;
	opening_hours: string;
	description: string;
	submission_notes: string;
	submission_reason: string;
	original_poi_id?: number;
}

const initialFormData: POISubmissionForm = {
	name: '',
	name_en: '',
	name_tr: '',
	name_uk: '',
	name_de: '',
	name_ru: '',
	name_ar: '',
	category: '',
	subcategory: '',
	cuisine: '',
	latitude: null,
	longitude: null,
	city: '',
	district: '',
	neighborhood: '',
	street: '',
	full_address: '',
	province: '',
	phone_number: '',
	opening_hours: '',
	description: '',
	submission_notes: '',
	submission_reason: 'new_poi',
	// original_poi_id is not included for new POI submissions
};

// Using shared constants from @/app/shared/poi
// categories = POI_CATEGORIES
// submissionReasons = POI_SUBMISSION_REASONS

export default function POISubmitPage() {
	const { data: session, status } = useSession();
	const router = useRouter();
	const [formData, setFormData] = useState<POISubmissionForm>(initialFormData);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [submitSuccess, setSubmitSuccess] = useState(false);
	const [submitError, setSubmitError] = useState<string | null>(null);
	const [isGettingLocation, setIsGettingLocation] = useState(false);
	const [isGeocodingAddress, setIsGeocodingAddress] = useState(false);

	// Redirect if not authenticated
	useEffect(() => {
		if (status === 'loading') return;
		if (!session) {
			router.push('/auth/signin?callbackUrl=/pois/submit');
		}
	}, [session, status, router]);

	const handleInputChange = (
		field: keyof POISubmissionForm,
		value: string | number
	) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const getCurrentLocation = () => {
		setIsGettingLocation(true);

		if (!navigator.geolocation) {
			setSubmitError('Geolocation is not supported by this browser');
			setIsGettingLocation(false);
			return;
		}

		navigator.geolocation.getCurrentPosition(
			async (position) => {
				const latitude = position.coords.latitude;
				const longitude = position.coords.longitude;

				setFormData((prev) => ({
					...prev,
					latitude,
					longitude,
				}));
				setIsGettingLocation(false);

				// Auto-fill address fields using reverse geocoding
				await reverseGeocode(latitude, longitude);
			},
			() => {
				setSubmitError(
					'Unable to get your location. Please enter coordinates manually.'
				);
				setIsGettingLocation(false);
			},
			{
				enableHighAccuracy: true,
				timeout: 10000,
				maximumAge: 60000,
			}
		);
	};

	const reverseGeocode = async (lat: number, lng: number) => {
		if (!lat || !lng) return;

		setIsGeocodingAddress(true);
		try {
			const response = await fetch(
				`/api/geocoding/reverse?lat=${lat}&lng=${lng}`
			);
			const data = await response.json();

			if (data.success) {
				setFormData((prev) => ({
					...prev,
					province: data.province || '',
					city: data.city || '',
					district: data.district || '',
					neighborhood: data.neighborhood || '',
					street: data.street || '',
					full_address: data.full_address || '',
				}));
			}
		} catch (error) {
			console.error('Reverse geocoding failed:', error);
		} finally {
			setIsGeocodingAddress(false);
		}
	};

	// Auto-fill address when coordinates change manually
	useEffect(() => {
		if (formData.latitude && formData.longitude) {
			const timeoutId = setTimeout(() => {
				reverseGeocode(formData.latitude!, formData.longitude!);
			}, 1000); // Debounce for 1 second

			return () => clearTimeout(timeoutId);
		}
	}, [formData.latitude, formData.longitude]);

	const validateForm = (): string | null => {
		if (!formData.name.trim()) return 'Name is required';
		if (!formData.category.trim()) return 'Category is required';
		if (!formData.latitude || !formData.longitude)
			return 'Location coordinates are required';
		if (formData.latitude < -90 || formData.latitude > 90)
			return 'Invalid latitude';
		if (formData.longitude < -180 || formData.longitude > 180)
			return 'Invalid longitude';
		return null;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		// Only require original_poi_id for submissions that are updates/reports of existing POIs
		// For new POI submissions, original_poi_id is not needed
		if (formData.submission_reason !== 'new_poi' && !formData.original_poi_id) {
			setSubmitError('original_poi_id is required for POI updates and reports');
			return;
		}

		const validationError = validateForm();
		if (validationError) {
			setSubmitError(validationError);
			return;
		}

		setIsSubmitting(true);
		setSubmitError(null);

		try {
			// Prepare form data - exclude original_poi_id for new POI submissions
			const submissionData = { ...formData };
			if (formData.submission_reason === 'new_poi') {
				delete submissionData.original_poi_id;
			}

			const response = await fetch('/api/pois/submit', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(submissionData),
			});

			const data = await response.json();

			if (data.success) {
				setSubmitSuccess(true);
				setFormData(initialFormData);
				logger.info('POI submitted successfully', {
					tempPoiId: data.temp_poi_id,
				});
			} else {
				setSubmitError(data.error || 'Failed to submit POI');
			}
		} catch (error) {
			setSubmitError('Network error. Please try again.');
			logger.error('Error submitting POI', { error });
		} finally {
			setIsSubmitting(false);
		}
	};

	if (status === 'loading') {
		return (
			<div className='min-h-screen flex items-center justify-center'>
				<FaSpinner className='animate-spin text-4xl text-blue-500' />
			</div>
		);
	}

	if (!session) {
		return null; // Will redirect
	}

	if (submitSuccess) {
		return (
			<div className='min-h-screen bg-gray-50 py-12'>
				<div className='max-w-2xl mx-auto px-4'>
					<div className='bg-white rounded-lg shadow-lg p-8 text-center'>
						<FaCheck className='text-green-500 text-6xl mx-auto mb-4' />
						<h1 className='text-3xl font-bold text-gray-900 mb-4'>
							POI Submitted Successfully!
						</h1>
						<p className='text-gray-600 mb-6'>
							Thank you for your submission. Your POI has been sent for admin
							review and will be published once approved.
						</p>
						<div className='space-y-4'>
							<button
								onClick={() => setSubmitSuccess(false)}
								className='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors'>
								Submit Another POI
							</button>
							<button
								onClick={() => router.push('/pois')}
								className='block w-full bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors'>
								Browse POIs
							</button>
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50 py-12'>
			<div className='max-w-4xl mx-auto px-4'>
				<div className='bg-white rounded-lg shadow-lg p-8'>
					<div className='mb-8'>
						<h1 className='text-3xl font-bold text-gray-900 mb-2'>
							Submit a New POI
						</h1>
						<p className='text-gray-600'>
							Help improve our database by submitting a new Point of Interest.
							All submissions are reviewed by our team before being published.
						</p>
					</div>

					{submitError && (
						<div className='mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center'>
							<FaTimes className='text-red-500 mr-3' />
							<span className='text-red-700'>{submitError}</span>
						</div>
					)}

					<form
						onSubmit={handleSubmit}
						className='space-y-8'>
						{/* Basic Information */}
						<div className='border-b border-gray-200 pb-8'>
							<h2 className='text-xl font-semibold text-gray-900 mb-4'>
								Basic Information
							</h2>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								{/* Name */}
								<div className='md:col-span-2'>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Name <span className='text-red-500'>*</span>
									</label>
									<input
										type='text'
										value={formData.name}
										onChange={(e) => handleInputChange('name', e.target.value)}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										placeholder='Enter POI name'
										required
									/>
								</div>

								{/* Category */}
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Category <span className='text-red-500'>*</span>
									</label>
									<select
										value={formData.category}
										onChange={(e) =>
											handleInputChange('category', e.target.value)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										required>
										<option value=''>Select a category</option>
										{POI_CATEGORIES.map((category) => (
											<option
												key={category}
												value={category}>
												{category}
											</option>
										))}
									</select>
								</div>

								{/* Subcategory */}
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Subcategory
									</label>
									<select
										value={formData.subcategory}
										onChange={(e) =>
											handleInputChange('subcategory', e.target.value)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'>
										<option value=''>Select a subcategory</option>
										{POI_SUBCATEGORY_OPTIONS.map((subcategory) => (
											<option
												key={subcategory}
												value={subcategory}>
												{subcategory.charAt(0).toUpperCase() +
													subcategory.slice(1)}
											</option>
										))}
									</select>
								</div>

								{/* Cuisine */}
								<div className='md:col-span-2'>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Cuisine Type
									</label>
									<input
										type='text'
										value={formData.cuisine}
										onChange={(e) =>
											handleInputChange('cuisine', e.target.value)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										placeholder='e.g., Turkish, Italian, Asian'
									/>
								</div>

								{/* Multilingual Names */}
								<div className='md:col-span-2'>
									<h3 className='text-lg font-medium text-gray-900 mb-3'>
										Multilingual Names (Optional)
									</h3>
									<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												English Name
											</label>
											<input
												type='text'
												value={formData.name_en}
												onChange={(e) =>
													handleInputChange('name_en', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='English name'
											/>
										</div>
										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												Turkish Name
											</label>
											<input
												type='text'
												value={formData.name_tr}
												onChange={(e) =>
													handleInputChange('name_tr', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Türkçe isim'
											/>
										</div>
										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												Ukrainian Name
											</label>
											<input
												type='text'
												value={formData.name_uk}
												onChange={(e) =>
													handleInputChange('name_uk', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Українська назва'
											/>
										</div>
										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												German Name
											</label>
											<input
												type='text'
												value={formData.name_de}
												onChange={(e) =>
													handleInputChange('name_de', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Deutscher Name'
											/>
										</div>
										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												Russian Name
											</label>
											<input
												type='text'
												value={formData.name_ru}
												onChange={(e) =>
													handleInputChange('name_ru', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Русское название'
											/>
										</div>
										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												Arabic Name
											</label>
											<input
												type='text'
												value={formData.name_ar}
												onChange={(e) =>
													handleInputChange('name_ar', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='الاسم العربي'
											/>
										</div>
									</div>
								</div>
							</div>
						</div>

						{/* Location Information */}
						<div className='border-b border-gray-200 pb-8'>
							<h2 className='text-xl font-semibold text-gray-900 mb-4 flex items-center'>
								<FaMapMarkerAlt className='mr-2 text-blue-500' />
								Location Information
							</h2>

							<div className='mb-4 flex items-center space-x-4'>
								<button
									type='button'
									onClick={getCurrentLocation}
									disabled={isGettingLocation}
									className='bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center'>
									{isGettingLocation ? (
										<FaSpinner className='animate-spin mr-2' />
									) : (
										<FaMapMarkerAlt className='mr-2' />
									)}
									{isGettingLocation
										? 'Getting Location...'
										: 'Use Current Location'}
								</button>

								{isGeocodingAddress && (
									<div className='flex items-center text-blue-600'>
										<FaSpinner className='animate-spin mr-2' />
										<span className='text-sm'>Auto-filling address...</span>
									</div>
								)}
							</div>

							<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Latitude *
									</label>
									<input
										type='number'
										step='any'
										value={formData.latitude || ''}
										onChange={(e) =>
											handleInputChange(
												'latitude',
												parseFloat(e.target.value) || 0
											)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										placeholder='e.g., 41.0082'
										required
									/>
								</div>

								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Longitude *
									</label>
									<input
										type='number'
										step='any'
										value={formData.longitude || ''}
										onChange={(e) =>
											handleInputChange(
												'longitude',
												parseFloat(e.target.value) || 0
											)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										placeholder='e.g., 28.9784'
										required
									/>
								</div>

								{/* Address Fields - Auto-filled */}
								<div className='md:col-span-2'>
									<h3 className='text-lg font-medium text-gray-900 mb-3 flex items-center'>
										Address Information
										{isGeocodingAddress && (
											<span className='ml-2 text-sm text-blue-600'>
												(Auto-filling...)
											</span>
										)}
									</h3>
									<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												Province
											</label>
											<input
												type='text'
												value={formData.province}
												onChange={(e) =>
													handleInputChange('province', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Auto-filled from coordinates'
											/>
										</div>

										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												City
											</label>
											<input
												type='text'
												value={formData.city}
												onChange={(e) =>
													handleInputChange('city', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Auto-filled from coordinates'
											/>
										</div>

										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												District
											</label>
											<input
												type='text'
												value={formData.district}
												onChange={(e) =>
													handleInputChange('district', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Auto-filled from coordinates'
											/>
										</div>

										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												Neighborhood
											</label>
											<input
												type='text'
												value={formData.neighborhood}
												onChange={(e) =>
													handleInputChange('neighborhood', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Auto-filled from coordinates'
											/>
										</div>

										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												Street
											</label>
											<input
												type='text'
												value={formData.street}
												onChange={(e) =>
													handleInputChange('street', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Auto-filled from coordinates'
											/>
										</div>

										<div>
											<label className='block text-sm font-medium text-gray-700 mb-2'>
												Full Address
											</label>
											<input
												type='text'
												value={formData.full_address}
												onChange={(e) =>
													handleInputChange('full_address', e.target.value)
												}
												className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
												placeholder='Auto-filled from coordinates'
											/>
										</div>
									</div>
								</div>

								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Neighborhood
									</label>
									<input
										type='text'
										value={formData.neighborhood}
										onChange={(e) =>
											handleInputChange('neighborhood', e.target.value)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										placeholder='Enter neighborhood'
									/>
								</div>

								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Province
									</label>
									<input
										type='text'
										value={formData.province}
										onChange={(e) =>
											handleInputChange('province', e.target.value)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										placeholder='Enter province'
									/>
								</div>
							</div>

							<div className='mt-6'>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Full Address
								</label>
								<input
									type='text'
									value={formData.full_address}
									onChange={(e) =>
										handleInputChange('full_address', e.target.value)
									}
									className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
									placeholder='Enter complete address'
								/>
							</div>
						</div>

						{/* Contact & Details */}
						<div className='border-b border-gray-200 pb-8'>
							<h2 className='text-xl font-semibold text-gray-900 mb-4 flex items-center'>
								<FaPhone className='mr-2 text-green-500' />
								Contact & Details
							</h2>

							<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2'>
										Phone Number
									</label>
									<input
										type='tel'
										value={formData.phone_number}
										onChange={(e) =>
											handleInputChange('phone_number', e.target.value)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										placeholder='e.g., +90 ************'
									/>
								</div>

								<div>
									<label className='block text-sm font-medium text-gray-700 mb-2 flex items-center'>
										<FaClock className='mr-1' />
										Opening Hours
									</label>
									<input
										type='text'
										value={formData.opening_hours}
										onChange={(e) =>
											handleInputChange('opening_hours', e.target.value)
										}
										className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
										placeholder='e.g., Mon-Fri 9:00-18:00, Sat 10:00-16:00'
									/>
								</div>
							</div>

							<div className='mt-6'>
								<label className='block text-sm font-medium text-gray-700 mb-2 flex items-center'>
									<FaInfoCircle className='mr-1' />
									Description
								</label>
								<textarea
									value={formData.description}
									onChange={(e) =>
										handleInputChange('description', e.target.value)
									}
									rows={4}
									className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
									placeholder='Provide a detailed description of this POI...'
								/>
							</div>
						</div>

						{/* Submission Notes */}
						<div className='pb-8'>
							<h2 className='text-xl font-semibold text-gray-900 mb-4'>
								Additional Notes
							</h2>

							<div>
								<label className='block text-sm font-medium text-gray-700 mb-2'>
									Submission Notes
								</label>
								<textarea
									value={formData.submission_notes}
									onChange={(e) =>
										handleInputChange('submission_notes', e.target.value)
									}
									rows={3}
									className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
									placeholder='Any additional information or notes for the review team...'
								/>
								<p className='text-sm text-gray-500 mt-1'>
									Optional: Provide any additional context or information that
									might help our review team.
								</p>
							</div>
						</div>

						{/* Submit Button */}
						<div className='flex justify-end space-x-4'>
							<button
								type='button'
								onClick={() => router.push('/pois')}
								className='px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors'>
								Cancel
							</button>
							<button
								type='submit'
								disabled={isSubmitting}
								className='bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center'>
								{isSubmitting ? (
									<>
										<FaSpinner className='animate-spin mr-2' />
										Submitting...
									</>
								) : (
									'Submit POI'
								)}
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	);
}
