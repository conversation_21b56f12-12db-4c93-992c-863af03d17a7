/** @format */

'use client';

import { colors } from '@/app/colors';
import dynamic from 'next/dynamic';
import React, {
	forwardRef,
	useCallback,
	useEffect,
	useImperativeHandle,
	useRef,
	useState,
} from 'react';
import POIRankingPanel from './components/POIRankingPanel';
import './globe.css';

// Global zoom constants - all functions use these values
const ZOOM_CONSTANTS = {
	INITIAL_ALTITUDE: 1.7,
	TRANSITION_THRESHOLD: 0.5, // Trigger transition to flat map
	MANUAL_RETURN_PROTECTION_THRESHOLD: 200, // Reset manual return protection
	SAFE_DISTANCE: 1.2, // Safe distance for manual return protection
} as const;

// Dynamically import Globe component to avoid SSR issues
const GlobeGL = dynamic(() => import('react-globe.gl'), {
	ssr: false,
	loading: () => (
		<div
			className='w-full h-full flex items-center justify-center'
			style={{
				background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
			}}>
			<div className='text-center'>
				<div
					className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 mx-auto mb-4'
					style={{
						borderTopColor: colors.brand.blue,
						borderBottomColor: colors.brand.blue,
						borderLeftColor: 'transparent',
						borderRightColor: 'transparent',
					}}></div>
				<p
					className='text-lg'
					style={{ color: colors.neutral.textBlack }}>
					Loading Interactive Globe...
				</p>
			</div>
		</div>
	),
});

interface POI {
	id: number;
	poi_type: string;
	name: string;
	category: string;
	subcategory: string;
	city: string;
	district: string;
	country?: string;
	latitude: number;
	longitude: number;
	random_score: number;
}

interface GlobeProps {
	isFlattened: boolean;
	isClient: boolean;
	countriesData: Array<Record<string, unknown>>;
	userMarker: Array<Record<string, unknown>>;
	currentZoom: number;
	currentLocation: { lat: number; lng: number } | null;
	userLocation: { lat: number; lng: number } | null;
	onZoomChange: (distance: number) => void;
	onLocationChange: (location: { lat: number; lng: number }) => void;
	onTransitionTrigger: () => void;
	onLocationInfoToggle: () => void;
	isManuallyReturning: boolean;
	isTransitioning: boolean;
	showNavButtons: boolean;
	manualReturnTime: number;
	isManuallyReturningRef: React.MutableRefObject<boolean>;
}

export interface GlobeRef {
	pointOfView: (
		pov: { lat: number; lng: number; altitude: number },
		duration?: number
	) => void;
	controls: () => GlobeControls | undefined;
	getCoords: (
		x: number,
		y: number
	) => { lat: number; lng: number } | null | undefined;
}

// Internal globe instance interface for react-globe.gl
interface GlobeControls {
	autoRotate: boolean;
	autoRotateSpeed: number;
	enableZoom: boolean;
	enableRotate: boolean;
	enablePan: boolean;
	minDistance: number;
	maxDistance: number;
	enableDamping: boolean;
	dampingFactor: number;
	rotateSpeed: number;
	zoomSpeed: number;
	addEventListener: (event: string, handler: () => void) => void;
	removeEventListener: (event: string, handler: () => void) => void;
}

interface GlobeInstance {
	pointOfView: (
		pov?: { lat: number; lng: number; altitude: number },
		duration?: number
	) => { lat: number; lng: number; altitude: number };
	controls: () => GlobeControls;
	getCoords: (
		x: number,
		y: number
	) => { lat: number; lng: number } | null | undefined;
	width: (width?: number) => GlobeInstance | number;
	height: (height?: number) => GlobeInstance | number;
	parentElement?: HTMLElement;
}

const Globe = forwardRef<GlobeRef, GlobeProps>(
	(
		{
			isFlattened,
			isClient,
			countriesData,
			userMarker,
			currentZoom,
			currentLocation,
			userLocation,
			onZoomChange,
			onLocationChange,
			onTransitionTrigger,
			onLocationInfoToggle,
			isManuallyReturning,
			isTransitioning,
			isManuallyReturningRef,
		},
		ref
	) => {
		const globeRef = useRef<GlobeInstance | null>(null);

		// Game-like state
		const [rankingPOIs, setRankingPOIs] = useState<POI[]>([]);
		const [showRankingPanel, setShowRankingPanel] = useState(false);
		const [selectedLocation, setSelectedLocation] = useState<{
			name: string;
			type: 'country' | 'city';
		} | null>(null);
		const [isLoadingRankings, setIsLoadingRankings] = useState(false);

		// Simple globe configuration
		const [globeColor, setGlobeColor] = useState<string>(colors.brand.blue);
		const globeTexture =
			'https://unpkg.com/three-globe/example/img/earth-day.jpg';

		// Smooth color transition effect
		useEffect(() => {
			// Example: cycle through brand/supporting colors every 10s
			const palette = [
				colors.brand.blue,
				colors.brand.navy,
				colors.brand.green,
				colors.supporting.lightBlue,
				colors.supporting.mintGreen,
			];
			let idx = 0;
			const interval = setInterval(() => {
				idx = (idx + 1) % palette.length;
				setGlobeColor(palette[idx]);
			}, 10000);
			return () => clearInterval(interval);
		}, []);

		// Expose globe methods to parent
		useImperativeHandle(ref, () => ({
			pointOfView: (
				pov: { lat: number; lng: number; altitude: number },
				duration?: number
			) => {
				if (globeRef.current) {
					globeRef.current.pointOfView(pov, duration);
				}
			},
			controls: () => globeRef.current?.controls(),
			getCoords: (x: number, y: number) => globeRef.current?.getCoords(x, y),
		}));

		// Game-like country coloring - only Turkey is unlocked
		const getCountryColor = (countryName: string) => {
			const name = countryName.toLowerCase();

			// Turkey is unlocked (bright colors)
			if (name.includes('turkey') || name.includes('türkiye')) {
				return '#4CAF50'; // Green for unlocked
			}

			// All other countries are locked (dark colors)
			return '#2C2C2C'; // Dark gray for locked
		};

		// Handle city selection from dropdown
		const handleCitySelect = useCallback((cityName: string) => {
			console.log('🏙️ City selected:', cityName);
			// You can add logic here to fetch POIs for the specific city
			// For now, we'll just log the selection
		}, []);

		// Fetch POI rankings for clicked location
		const fetchLocationRankings = useCallback(
			async (locationName: string, locationType: 'country' | 'city') => {
				if (!currentLocation) {
					console.warn('No current location available');
					return;
				}

				console.log('🚀 Fetching rankings for:', locationName, locationType);

				try {
					const response = await fetch('/api/pois/rankings', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({
							locationType,
							locationName,
							center: currentLocation,
							zoom: currentZoom,
							offset: 0,
							limit: 20,
							country: locationType === 'country' ? locationName : undefined,
						}),
					});

					if (response.ok) {
						const data = await response.json();
						if (data.success) {
							console.log(
								'✅ Rankings loaded:',
								data.pois?.length || 0,
								'POIs'
							);
							setRankingPOIs(data.pois || []);
							// Panel is already shown from click handler
						} else {
							console.error('❌ API returned error:', data.error);
							setShowRankingPanel(false);
						}
					} else {
						console.error('❌ API request failed:', response.status);
						setShowRankingPanel(false);
					}
				} catch (error) {
					console.error('❌ Failed to fetch location rankings:', error);
					setShowRankingPanel(false);
				} finally {
					setIsLoadingRankings(false);
				}
			},
			[currentLocation, currentZoom]
		);

		// Handle country click
		const handlePolygonClick = useCallback(
			(polygon: { properties?: Record<string, unknown> }) => {
				console.log('🎯 Polygon clicked:', polygon);

				const countryName = String(
					polygon.properties?.NAME ||
						polygon.properties?.name ||
						polygon.properties?.NAME_EN ||
						''
				);

				if (!countryName) {
					console.warn('No country name found in polygon:', polygon);
					return;
				}

				// For now, only allow interactions with Turkey (unlocked country)
				const isTurkey =
					countryName.toLowerCase().includes('turkey') ||
					countryName.toLowerCase().includes('türkiye');

				if (isTurkey) {
					console.log('🇹🇷 Turkey clicked, fetching rankings...');
					// Use English name - database function now handles both English and Turkish
					const countryNameForAPI = 'Turkey';

					// Immediately show loading state and panel
					setIsLoadingRankings(true);
					setShowRankingPanel(true);
					setSelectedLocation({ name: countryNameForAPI, type: 'country' });
					setRankingPOIs([]); // Clear previous data

					// Fetch data with a small delay to ensure UI updates first
					setTimeout(() => {
						fetchLocationRankings(countryNameForAPI, 'country');
					}, 50);
				} else {
					// Show locked country message
					alert(
						`🔒 ${countryName} is locked! Only Turkey is currently unlocked in this version.`
					);
				}
			},
			[fetchLocationRankings]
		);

		// Configure globe
		useEffect(() => {
			if (!isClient) return;

			const initializeGlobe = () => {
				const globe = globeRef.current;
				if (!globe) return false;

				try {
					if (!globe.controls || typeof globe.controls !== 'function') {
						return false;
					}

					// Configure interactive globe settings
					const controls = globe.controls();
					controls.enableZoom = true;
					controls.enableRotate = true;
					controls.autoRotate = false;
					controls.minDistance = 60;
					controls.maxDistance = 800;
					controls.enablePan = true;
					controls.enableDamping = true;
					controls.dampingFactor = 0.1;
					controls.rotateSpeed = 0.5;
					controls.zoomSpeed = 1.0;

					// Set initial position
					const initialLat = userLocation?.lat || currentLocation?.lat || 0;
					const initialLng = userLocation?.lng || currentLocation?.lng || 0;

					globe.pointOfView({
						lat: initialLat,
						lng: initialLng,
						altitude: ZOOM_CONSTANTS.INITIAL_ALTITUDE,
					});

					// Set initial size to full screen
					const updateGlobeSize = () => {
						if (globe && globeRef.current?.parentElement) {
							const width = window.innerWidth;
							const height = window.innerHeight;
							globe.width(width);
							globe.height(height);
						}
					};

					updateGlobeSize();

					// Handle window resize
					const handleResize = () => {
						updateGlobeSize();
					};

					window.addEventListener('resize', handleResize);

					// Handle view changes (zoom, rotation, pan) with throttling
					let lastUpdate = 0;
					const handleViewChange = () => {
						const now = Date.now();
						if (now - lastUpdate < 50) return; // Throttle to 20fps
						lastUpdate = now;

						if (!globe) return;

						const pov = globe.pointOfView();
						const distance = pov.altitude;

						// Debug zoom level
						console.log('Current zoom level:', distance);

						onZoomChange(distance);

						// Update current view coordinates on any change
						try {
							const center = globe.getCoords(
								window.innerWidth / 2,
								window.innerHeight / 2
							);
							if (
								center &&
								typeof center.lat === 'number' &&
								typeof center.lng === 'number'
							) {
								onLocationChange({ lat: center.lat, lng: center.lng });
							}
						} catch (error) {
							console.warn('Failed to get globe coordinates:', error);
						}

						// Direct transition trigger as backup for very close zoom
						const isCurrentlyReturning =
							isManuallyReturning || isManuallyReturningRef.current;

						if (
							distance < ZOOM_CONSTANTS.TRANSITION_THRESHOLD &&
							!isFlattened &&
							!isTransitioning &&
							!isCurrentlyReturning
						) {
							console.log(
								'🚀 Direct transition trigger at distance:',
								distance
							);
							onTransitionTrigger();
						} else if (
							isCurrentlyReturning &&
							distance < ZOOM_CONSTANTS.TRANSITION_THRESHOLD
						) {
							console.log(
								'🛡️ Auto-transition blocked - manual return protection active'
							);
						}
					};

					// Add event listener
					globe.controls().addEventListener('change', handleViewChange);

					const resizeObserver = new ResizeObserver(() => {
						updateGlobeSize();
					});

					const container = globeRef.current?.parentElement;
					if (container) resizeObserver.observe(container);

					// Return cleanup function
					const cleanup = () => {
						window.removeEventListener('resize', handleResize);
						globe.controls().removeEventListener('change', handleViewChange);
						resizeObserver.disconnect();
					};

					return cleanup;
				} catch {
					return false;
				}
			};

			// Try to initialize immediately
			let cleanup = initializeGlobe();

			// If initialization failed, retry with intervals
			if (!cleanup) {
				const retryInterval = setInterval(() => {
					cleanup = initializeGlobe();
					if (cleanup) {
						clearInterval(retryInterval);
					}
				}, 100);

				return () => {
					clearInterval(retryInterval);
					if (cleanup && typeof cleanup === 'function') {
						cleanup();
					}
				};
			}

			return cleanup;
		}, [isClient]); // Only re-initialize when client state changes

		if (!isClient) return null;

		return (
			<>
				<div
					className={`absolute inset-0 w-full h-full transition-all duration-1000 ease-in-out ${
						isFlattened
							? 'opacity-0 pointer-events-none scale-110'
							: 'opacity-100 scale-100'
					}`}>
					<div
						className='w-full h-full globe-container'
						style={{ minHeight: 320, minWidth: 320 }}>
						<GlobeGL
							// eslint-disable-next-line @typescript-eslint/no-explicit-any
							ref={globeRef as React.MutableRefObject<any>}
							globeImageUrl={globeTexture}
							backgroundColor='rgba(0, 0, 0, 0)'
							showGlobe={true}
							showAtmosphere={true}
							atmosphereColor={globeColor + '33'}
							atmosphereAltitude={0.18}
							globeMaterial={{
								transparent: true,
								opacity: 0.92,
								color: globeColor,
								transition: 'color 1.5s cubic-bezier(0.4,0,0.2,1)',
							}}
							enablePointerInteraction={true}
							rendererConfig={{
								antialias: true,
								alpha: true,
								shadowMap: {
									enabled: true,
								},
							}}
							animateIn={true}
							width={undefined}
							height={undefined}
							// Country polygons with game-like colors
							polygonsData={countriesData}
							polygonCapColor={(d: {
								properties?: Record<string, unknown>;
							}) => {
								const countryName = String(
									d.properties?.NAME ||
										d.properties?.name ||
										d.properties?.NAME_EN ||
										'unknown'
								);
								return getCountryColor(countryName);
							}}
							polygonSideColor={(d: {
								properties?: Record<string, unknown>;
							}) => {
								const countryName = String(
									d.properties?.NAME ||
										d.properties?.name ||
										d.properties?.NAME_EN ||
										'unknown'
								);
								return getCountryColor(countryName);
							}}
							polygonStrokeColor={() => {
								return '#FFFFFF'; // White boundaries for all countries
							}}
							polygonAltitude={() => 0.01}
							onPolygonClick={(polygon: {
								properties?: Record<string, unknown>;
							}) => {
								console.log('🎯 Globe polygon clicked:', polygon);
								// Handle country click
								handlePolygonClick(polygon);
							}}
							hexPolygonResolution={3}
							// Only user location marker (no POI markers)
							pointsData={userMarker}
							pointAltitude={() => 0.08}
							pointRadius={() => {
								const baseRadius = 0.3;
								const zoomFactor = Math.max(0.1, currentZoom / 800);
								return baseRadius * zoomFactor;
							}}
							pointColor={() => '#3B82F6'}
							pointResolution={12}
							pointLabel={() => 'Your Location - Click for details'}
							onPointClick={() => onLocationInfoToggle()}
							// Remove location labels to avoid screen-fixed text
							labelsData={[]}
							labelText={() => ''}
							labelSize={() => 0}
							labelAltitude={() => 0}
							labelColor={() => 'transparent'}
							labelResolution={0}
							labelIncludeDot={false}
							// Remove country name labels to avoid screen-fixed text
							polygonLabel={() => ''}
						/>
					</div>
				</div>

				{/* POI Ranking Panel */}
				<POIRankingPanel
					isVisible={showRankingPanel}
					onClose={() => setShowRankingPanel(false)}
					locationName={selectedLocation?.name || ''}
					locationType={selectedLocation?.type || 'country'}
					pois={rankingPOIs}
					isLoading={isLoadingRankings}
					onCitySelect={handleCitySelect}
					hasMoreData={true}
				/>
			</>
		);
	}
);

Globe.displayName = 'Globe';

export default Globe;
