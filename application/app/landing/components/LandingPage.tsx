/** @format */

import { colors } from '@/app/colors';
import React from 'react';
import CreditsSection from './CreditsSection';
import CTASection from './CTASection';
import FeatureSection from './FeatureSection';
import Footer from './Footer';
import HeroSection from './HeroSection';
import HowItWorksSection from './HowItWorksSection';

interface LandingPageProps {
	onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
	return (
		<div
			className='min-h-screen relative'
			style={{
				background: `
          linear-gradient(180deg,
            rgba(51, 194, 255, 0.3) 0%,
            rgba(102, 208, 255, 0.3) 25%,
            rgba(128, 237, 153, 0.3) 50%,
            rgba(163, 247, 181, 0.3) 75%,
            rgba(1, 3, 79, 0.3) 100%
          ),
          ${colors.neutral.cloudWhite}
        `,
			}}>
			{/* Main Content */}
			<HeroSection onGetStarted={onGetStarted} />
			<FeatureSection />
			<HowItWorksSection />
			<CreditsSection onGetStarted={onGetStarted} />
			<CTASection onGetStarted={onGetStarted} />
			<Footer />
		</div>
	);
};

export default LandingPage;
