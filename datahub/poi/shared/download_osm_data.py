#!/usr/bin/env python3
"""
Shared OSM Data Downloader

Downloads Turkey OSM data for POI extraction.
This script is shared across all POI types.
"""

import requests
import os
import sys
from pathlib import Path
import logging
from config import TURKEY_OSM_PBF_PATH, RAW_DATA_DIR

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("osm_downloader")


def download_turkey_osm():
    """Download Turkey OSM data if not already present"""

    # Check if file already exists
    if TURKEY_OSM_PBF_PATH.exists():
        file_size = TURKEY_OSM_PBF_PATH.stat().st_size
        if file_size > 100 * 1024 * 1024:  # At least 100MB
            logger.info(
                f"Turkey OSM file already exists ({file_size / 1024 / 1024:.1f} MB)")
            return True
        else:
            logger.warning(
                f"Existing file is too small ({file_size} bytes), re-downloading...")
            TURKEY_OSM_PBF_PATH.unlink()

    # Ensure raw data directory exists
    RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)

    # Download URL for Turkey OSM data (Turkey is in Europe section on Geofabrik)
    download_url = "https://download.geofabrik.de/europe/turkey-latest.osm.pbf"

    logger.info(f"Downloading Turkey OSM data from {download_url}")
    logger.info(
        f"This may take 5-15 minutes depending on your internet connection...")

    try:
        response = requests.get(download_url, stream=True)
        response.raise_for_status()

        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0

        with open(TURKEY_OSM_PBF_PATH, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)

                    # Progress indicator
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(
                            f"\rProgress: {progress:.1f}% ({downloaded_size / 1024 / 1024:.1f} MB)", end='')
                    else:
                        print(
                            f"\rDownloaded: {downloaded_size / 1024 / 1024:.1f} MB", end='')

        print()  # New line after progress

        # Verify download
        final_size = TURKEY_OSM_PBF_PATH.stat().st_size
        logger.info(
            f"Download complete! File size: {final_size / 1024 / 1024:.1f} MB")

        if final_size < 50 * 1024 * 1024:  # Less than 50MB is suspicious
            logger.error(
                "Downloaded file seems too small. Please check your internet connection.")
            return False

        return True

    except requests.exceptions.RequestException as e:
        logger.error(f"Download failed: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during download: {e}")
        return False


def main():
    """Main function for standalone execution"""
    logger.info("Starting Turkey OSM data download...")

    if download_turkey_osm():
        logger.info("OSM data download completed successfully!")
        return 0
    else:
        logger.error("OSM data download failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
