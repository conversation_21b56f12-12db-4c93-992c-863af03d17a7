#!/usr/bin/env python3
"""
Clean Cafe & Restaurant POI Data

Comprehensive cleaning script that handles:
- Basic validation (names, coordinates)
- Data standardization
- Duplicate removal
- Country information addition
- Phone number cleaning

This script only cleans data - no merging is performed here.
"""

from poi.shared.utils import has_any_name, has_lat_lon, validate_poi_data
from poi.shared.config import NAME_FIELDS
from .cafe_restaurant_config import POI_OUTPUT_DIR
import pandas as pd
import os
import sys
from pathlib import Path
import logging
from tqdm import tqdm

# Add shared modules to path - adjust for running from datahub directory
shared_path = Path(__file__).resolve().parent.parent / "shared"
sys.path.insert(0, str(shared_path))
sys.path.insert(0, str(Path(__file__).resolve().parent))

# Now import after path setup

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s %(levelname)s %(message)s')
logger = logging.getLogger("cafe_restaurant_cleaner")


def add_country_info(df):
    """Add country information to dataframe"""
    logger.info("Adding country information...")

    if 'country' not in df.columns:
        df['country'] = ''

    # Add country info for rows that don't have it
    # Since all POIs are from Türkiye (extracted from Turkish cities), set to Türkiye
    missing_country = df['country'].isna() | (df['country'] == '')
    if missing_country.any():
        logger.info(
            f"Setting country to 'Türkiye' for {missing_country.sum()} rows...")
        df.loc[missing_country, 'country'] = 'Türkiye'

    return df


def clean_phone_numbers(df):
    """Clean and standardize phone numbers"""
    logger.info("Cleaning phone numbers...")

    if 'phone_number' in df.columns:
        # Remove common prefixes and clean format
        df['phone_number'] = df['phone_number'].astype(str)
        df['phone_number'] = df['phone_number'].str.replace(
            r'^tel:', '', regex=True)
        df['phone_number'] = df['phone_number'].str.replace(
            r'^phone:', '', regex=True)
        df['phone_number'] = df['phone_number'].str.replace(
            r'[^\d\+\-\(\)\s]', '', regex=True)
        df['phone_number'] = df['phone_number'].str.strip()

        # Replace 'nan' strings with empty
        df.loc[df['phone_number'] == 'nan', 'phone_number'] = ''

    return df


def clean_opening_hours(df):
    """Clean and standardize opening hours"""
    logger.info("Cleaning opening hours...")

    if 'opening_hours' in df.columns:
        df['opening_hours'] = df['opening_hours'].astype(str)
        df.loc[df['opening_hours'] == 'nan', 'opening_hours'] = ''
        df['opening_hours'] = df['opening_hours'].str.strip()

    return df


def remove_duplicates(df):
    """Remove duplicate POIs based on coordinates"""
    logger.info("Removing duplicates...")

    initial_count = len(df)

    # Remove duplicates based on latitude and longitude
    df_clean = df.drop_duplicates(
        subset=['latitude', 'longitude'], keep='first')

    removed_count = initial_count - len(df_clean)
    if removed_count > 0:
        logger.info(f"Removed {removed_count} duplicate POIs")

    return df_clean


def clean_city_data(city_dir):
    """Clean all CSV files for a specific city"""
    city_name = city_dir.name
    logger.info(f"Cleaning data for {city_name}...")

    csv_files = list(city_dir.glob("*.csv"))
    if not csv_files:
        logger.warning(f"No CSV files found in {city_dir}")
        return

    total_cleaned = 0

    for csv_file in csv_files:
        try:
            # Read the CSV file
            df = pd.read_csv(csv_file)
            initial_count = len(df)

            if initial_count == 0:
                logger.info(f"Skipping empty file: {csv_file}")
                continue

            logger.info(f"Processing {csv_file.name}: {initial_count} rows")

            # Apply cleaning steps
            df = validate_poi_data(df)  # Basic validation from utils
            df = add_country_info(df)
            df = clean_phone_numbers(df)
            df = clean_opening_hours(df)
            df = remove_duplicates(df)

            # Save cleaned data back to the same file
            df.to_csv(csv_file, index=False, encoding='utf-8')

            final_count = len(df)
            cleaned_count = initial_count - final_count
            total_cleaned += cleaned_count

            logger.info(
                f"Cleaned {csv_file.name}: {initial_count} -> {final_count} rows ({cleaned_count} removed)")

        except Exception as e:
            logger.error(f"Error processing {csv_file}: {e}")

    logger.info(
        f"Completed cleaning for {city_name}: {total_cleaned} total rows cleaned")


def main():
    """Main cleaning function"""
    logger.info("Starting cafe & restaurant data cleaning...")
    logger.info("This script only cleans data - no merging is performed")

    if not POI_OUTPUT_DIR.exists():
        logger.error(f"Output directory not found: {POI_OUTPUT_DIR}")
        logger.error("Please run the extraction script first")
        return 1

    # Get all city directories
    city_dirs = [d for d in POI_OUTPUT_DIR.iterdir() if d.is_dir()
                 and d.name != '__pycache__']

    if not city_dirs:
        logger.error("No city directories found")
        return 1

    logger.info(f"Found {len(city_dirs)} cities to clean")

    # Process each city with progress bar
    with tqdm(total=len(city_dirs), desc="Cleaning cities", unit="city") as pbar:
        for city_dir in city_dirs:
            pbar.set_description(f"Cleaning {city_dir.name}")
            clean_city_data(city_dir)
            pbar.update(1)

    logger.info("Data cleaning completed!")
    logger.info("You can now run merge_data.py to combine all cleaned data")
    return 0


if __name__ == "__main__":
    sys.exit(main())
