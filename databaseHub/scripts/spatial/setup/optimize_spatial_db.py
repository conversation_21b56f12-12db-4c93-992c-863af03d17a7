#!/usr/bin/env python3
"""
Optimize spatial database: vacuum, analyze, and refresh materialized views.
"""
import psycopg2
import os
from dotenv import load_dotenv
from pathlib import Path

# Load .env from databaseHub root
env_path = Path(__file__).parent.parent / '.env'
load_dotenv(dotenv_path=env_path)

DB_CONFIG = {
    'dbname': os.getenv('DB_NAME'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'host': os.getenv('DB_HOST'),
    'port': os.getenv('DB_PORT', '5432')
}

def optimize():
    conn = psycopg2.connect(**DB_CONFIG)
    conn.autocommit = True
    cursor = conn.cursor()
    print("Running VACUUM ANALYZE on all spatial_schema tables...")
    for table in [
        'spatial_schema.pois',
        'spatial_schema.admin_boundaries',
        'spatial_schema.roads',
        'spatial_schema.poi_tags',
        'spatial_schema.community_reviews',
        'spatial_schema.verification_tasks',
        'spatial_schema.master_categories',
        'spatial_schema.poi_category_votes',
        'spatial_schema.poi_categories']:
        print(f"Vacuuming {table}...")
        cursor.execute(f"VACUUM ANALYZE {table};")
    print("Refreshing materialized views...")
    cursor.execute("SELECT refresh_materialized_views();")
    print("Optimization complete.")
    cursor.close()
    conn.close()

if __name__ == '__main__':
    optimize() 