# Superuser Bootstrap Guide

This guide explains how to create the first superuser in the system, since superusers are needed to access the admin panel and promote other users.

## Overview

The system has a 3-tier user role hierarchy:

1. **Superuser** (highest level)
   - Access to admin panel (`/admin/dashboard`)
   - Access to agent dashboard (`/agent/dashboard`)
   - Can promote users to agents
   - Can promote users to superusers
   - Full system administration privileges

2. **Agent** (middle level)
   - Access to agent dashboard (`/agent/dashboard`)
   - Can review and manage POI submissions
   - Special features not available to regular users

3. **User** (base level)
   - Standard user features only
   - Can sign up normally through the registration form

## The Bootstrap Problem

Since only superusers can access the admin panel to promote other users, you need to manually create the first superuser directly in the database.

## Prerequisites

1. A user must already exist in the system (signed up through normal registration)
2. Access to the PostgreSQL database
3. The database schema migrations must be applied (including `09_superuser_role_system.sql`)

## Step-by-Step Instructions

### 1. Apply Database Migrations

First, ensure all migrations are applied, especially the superuser role system:

```bash
# Navigate to the project root
cd /path/to/your/project

# Apply the superuser migration
psql -U wizlop_user -h localhost -d wizlop_db -f databaseHub/schema/09_superuser_role_system.sql
```

### 2. Find the User to Promote

Connect to your database and find the user you want to make a superuser:

```bash
psql -U wizlop_user -h localhost -d wizlop_db
```

```sql
-- List all users to find the one you want to promote
SELECT id, email, name, username, role, created_at 
FROM backend_schema.nextauth_users 
ORDER BY created_at DESC;
```

### 3. Promote User to Superuser

Use the user's email address to promote them to superuser:

```sql
-- Replace '<EMAIL>' with the actual email
UPDATE backend_schema.nextauth_users 
SET 
    role = 'superuser',
    superuser_permissions = '["admin.full_access", "agent.manage", "user.manage", "system.configure", "audit.view"]',
    superuser_activated_at = NOW()
WHERE email = '<EMAIL>';
```

### 4. Verify the Promotion

Check that the promotion was successful:

```sql
-- Verify the superuser was created
SELECT id, email, name, role, superuser_permissions, superuser_activated_at 
FROM backend_schema.nextauth_users 
WHERE role = 'superuser';
```

### 5. Test Access

1. Log out of the application if you're currently logged in
2. Log back in with the promoted user's credentials
3. Navigate to `/admin/dashboard` - you should now have access
4. Navigate to `/agent/dashboard` - you should also have access

## Alternative Method: Using User ID

If you know the user's ID instead of email:

```sql
-- Replace 'user-id-here' with the actual user ID
UPDATE backend_schema.nextauth_users 
SET 
    role = 'superuser',
    superuser_permissions = '["admin.full_access", "agent.manage", "user.manage", "system.configure", "audit.view"]',
    superuser_activated_at = NOW()
WHERE id = 'user-id-here';
```

## Superuser Permissions Explained

The default superuser permissions include:

- `admin.full_access`: Full administrative access to all system features
- `agent.manage`: Manage agent users and their permissions
- `user.manage`: Manage regular users and promote to agents
- `system.configure`: Configure system settings and parameters
- `audit.view`: View system audit logs and activity

## After Bootstrap

Once you have your first superuser:

1. **Access the Admin Panel**: Go to `/admin/dashboard`
2. **Search for Users**: Use the search functionality to find users by email, name, username, or ID
3. **Promote Users**: Click the promotion buttons to make users agents or superusers
4. **Manage the System**: Use the admin interface for all future user management

## Security Notes

1. **Limit Superusers**: Only create superusers for trusted administrators
2. **Regular Audits**: Periodically review who has superuser access
3. **Secure Database Access**: Ensure database access is properly secured
4. **Log Monitoring**: Monitor the agent activity logs for suspicious activity

## Troubleshooting

### Migration Errors

If you get errors applying the migration:

```bash
# Check if the migration was already applied
psql -U wizlop_user -h localhost -d wizlop_db -c "\d backend_schema.nextauth_users"

# Look for the superuser-related columns:
# - role (should include 'superuser' in CHECK constraint)
# - superuser_permissions
# - superuser_activated_at
# - superuser_activated_by
```

### User Not Found

If the UPDATE command affects 0 rows:

```sql
-- Double-check the user exists
SELECT id, email, name FROM backend_schema.nextauth_users WHERE email = '<EMAIL>';
```

### Access Still Denied

If you still can't access the admin panel:

1. Clear browser cache and cookies
2. Log out and log back in
3. Check browser console for JavaScript errors
4. Verify the user's role in the database:

```sql
SELECT id, email, role, superuser_permissions FROM backend_schema.nextauth_users WHERE email = '<EMAIL>';
```

## Example Complete Session

```bash
# Connect to database
psql -U wizlop_user -h localhost -d wizlop_db

# Find your user
SELECT id, email, name, role FROM backend_schema.nextauth_users WHERE email = '<EMAIL>';

# Promote to superuser
UPDATE backend_schema.nextauth_users 
SET 
    role = 'superuser',
    superuser_permissions = '["admin.full_access", "agent.manage", "user.manage", "system.configure", "audit.view"]',
    superuser_activated_at = NOW()
WHERE email = '<EMAIL>';

# Verify
SELECT id, email, role, superuser_permissions FROM backend_schema.nextauth_users WHERE role = 'superuser';

# Exit
\q
```

Now you can log in and access `/admin/dashboard` to manage other users!
