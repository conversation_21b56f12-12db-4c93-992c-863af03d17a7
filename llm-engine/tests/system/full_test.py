import requests
import json

BASE_URL = "http://127.0.0.1:8000"
USER_ID = "said1"
LATITUDE = 41.042521
LONGITUDE = 29.004538
SEARCH_RADIUS = 1600
NUM_CANDIDATES = 3

# Step 1: Create a session
session_response = requests.post(
    f"{BASE_URL}/session", json={"user_id": USER_ID})
session_data = session_response.json()
session_id = session_data.get("session_id")

print(f"\n🟢 Session ID: {session_id}\n")
print("=" * 60)

# Step 2: Message list
messages = [
    "Hello, how are you? i want to ask you something",
    "is there a cafe close to me! somewhere cozy and nice view",
    "can you share the lon/lat of Kırmızı Kedi Café",
    "okay then search for a restauran next to Kırmızı Kedi Café",
    "can you search for a cafe in maslak",
    "is there a restaurant next to AKM Sanat Cafe",
    "is there a market near me"

    # Multi-step test cases
    "find a restaurant in Besiktas and then a cafe next to it",
    "I want to find cafes in Beyoglu or restaurants in maslak",
    "search for a cafe close to saltbae and also find pharmacies in the same area",

    # Complex multi-step with POI reference
    "find a restaurant next to Kırmızı Kedi Café and then search for cafe in esenyurt"


]

# Step 3: Send messages
for i, msg in enumerate(messages, start=1):
    payload = {
        "user_id": USER_ID,
        "session_id": session_id,
        "message": msg,
        "latitude": LATITUDE,
        "longitude": LONGITUDE,
        "search_radius": SEARCH_RADIUS,
        "num_candidates": NUM_CANDIDATES
    }
    response = requests.post(f"{BASE_URL}/message", json=payload)
    response_data = response.json()

    print(f"📨 Message {i}: {msg}")

    # Display session title immediately if it exists
    if "session_title" in response_data and response_data["session_title"]:
        print(f"🏷️ Session Title: {response_data['session_title']}")

    print("🧾 Response:")
    print(json.dumps(response_data, indent=4, ensure_ascii=False))
    print("=" * 60)

# Step 4: Get session messages
messages_url = f"{BASE_URL}/messages"
messages_payload = {
    "user_id": USER_ID,
    "session_id": session_id
}
messages_response = requests.post(messages_url, json=messages_payload)

print("=" * 60)
print(f"\n📩 Session Messages ({messages_url}):")
print(json.dumps(messages_response.json(), indent=4, ensure_ascii=False))

# Step 5: Test health check endpoint
health_url = f"{BASE_URL}/health"
try:
    health_response = requests.get(health_url)
    print("=" * 60)
    print(f"\n🏥 Health Check ({health_url}):")
    print(f"Status Code: {health_response.status_code}")
    print(f"Response Text: {health_response.text}")

    if health_response.status_code == 200 and health_response.text.strip():
        try:
            print(json.dumps(health_response.json(), indent=4, ensure_ascii=False))
        except json.JSONDecodeError:
            print("❌ Invalid JSON response from health endpoint")
    else:
        print("❌ Health endpoint returned empty or error response")
except requests.exceptions.ConnectionError:
    print("❌ Cannot connect to server. Is the API server running?")
except Exception as e:
    print(f"❌ Health check error: {e}")

# Step 6: Get all session titles
titles_url = f"{BASE_URL}/titles"
titles_payload = {
    "user_id": USER_ID
}
titles_response = requests.post(titles_url, json=titles_payload)

# Step 7: Test delete endpoint
delete_url = f"{BASE_URL}/delete"
delete_payload = {
    "user_id": USER_ID,
    "session_id": session_id
}
try:
    delete_response = requests.post(delete_url, json=delete_payload)
    print("=" * 60)
    print(f"\n🗑️ Delete Session ({delete_url}):")
    print(f"Status Code: {delete_response.status_code}")
    if delete_response.status_code == 200 and delete_response.text.strip():
        try:
            print(json.dumps(delete_response.json(), indent=4, ensure_ascii=False))
        except json.JSONDecodeError:
            print("❌ Invalid JSON response from delete endpoint")
            print(f"Response Text: {delete_response.text}")
    else:
        print("❌ Delete endpoint returned empty or error response")
        print(f"Response Text: {delete_response.text}")
except Exception as e:
    print(f"❌ Delete endpoint error: {e}")


print("=" * 60)
print(f"\n📋 Session Titles ({titles_url}):")
print(f"Status Code: {titles_response.status_code}")
print(json.dumps(titles_response.json(), indent=4, ensure_ascii=False))
