"""
Execution Planner for Multi-Task Orchestrator.

This module plans and executes tasks in parallel or sequential order,
handling dependencies and context passing between tasks.
"""

import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional
from datetime import datetime
from src.infrastructure.log.unified_logger import debug, info, warning, error
from ..processes.shared import Task, TaskResult, ExecutionPlan
from ..processes import load_function


class ExecutionPlanner:
    """
    Plans and executes tasks based on dependencies and execution strategy.

    Supports:
    - Parallel execution for independent tasks
    - Sequential execution for dependent tasks
    - Context passing between dependent tasks
    - Error handling and recovery
    """

    def __init__(self, max_parallel_workers: int = 4):
        """
        Initialize the execution planner.

        Args:
            max_parallel_workers: Maximum number of parallel workers
        """
        self.max_parallel_workers = max_parallel_workers

    def plan(self, tasks: List[Task]) -> ExecutionPlan:
        """
        Create execution plan based on task dependencies.

        Args:
            tasks: List of tasks to plan

        Returns:
            ExecutionPlan with parallel groups and sequential chains
        """
        debug(f"Planning execution for {len(tasks)} tasks")

        parallel_groups = []
        sequential_chains = []

        # Separate independent and dependent tasks
        independent_tasks = [t for t in tasks if not t.dependencies]
        dependent_tasks = [t for t in tasks if t.dependencies]

        # Build dependency chains for sequential execution first
        tasks_in_chains = set()
        for task in dependent_tasks:
            chain = self._build_dependency_chain(task, tasks)
            if chain:
                sequential_chains.append(chain)
                # Track all tasks that are part of dependency chains
                for chain_task in chain:
                    tasks_in_chains.add(chain_task.task_id)
                debug(f"Created sequential chain with {len(chain)} tasks")

        # Group truly independent tasks for parallel execution
        # Exclude tasks that are part of dependency chains
        truly_independent_tasks = [
            t for t in independent_tasks if t.task_id not in tasks_in_chains]
        if truly_independent_tasks:
            parallel_groups.append(truly_independent_tasks)
            debug(
                f"Created parallel group with {len(truly_independent_tasks)} independent tasks")

        plan = ExecutionPlan(
            parallel_groups=parallel_groups,
            sequential_chains=sequential_chains
        )

        info(
            f"Execution plan: {len(parallel_groups)} parallel groups, {len(sequential_chains)} sequential chains")
        return plan

    def execute(self, plan: ExecutionPlan) -> List[TaskResult]:
        """
        Execute tasks according to the execution plan.

        Args:
            plan: ExecutionPlan to execute

        Returns:
            List of TaskResult objects
        """
        info(f"Executing plan with {plan.total_tasks} total tasks")
        all_results = []

        # Execute parallel groups
        for i, group in enumerate(plan.parallel_groups):
            info(f"Executing parallel group {i+1} with {len(group)} tasks")
            group_results = self._execute_parallel(group)
            all_results.extend(group_results)

        # Execute sequential chains
        for i, chain in enumerate(plan.sequential_chains):
            info(f"Executing sequential chain {i+1} with {len(chain)} tasks")
            chain_results = self._execute_sequential(chain, all_results)
            all_results.extend(chain_results)

        successful_tasks = sum(1 for r in all_results if r.success)
        info(
            f"Execution completed: {successful_tasks}/{len(all_results)} tasks successful")

        return all_results

    def _execute_parallel(self, tasks: List[Task]) -> List[TaskResult]:
        """Execute multiple tasks simultaneously."""
        if not tasks:
            return []

        debug(f"Starting parallel execution of {len(tasks)} tasks")
        results = []

        # Limit workers to available tasks or max workers
        max_workers = min(len(tasks), self.max_parallel_workers)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_task = {}
            for task in tasks:
                future = executor.submit(self._execute_single_task, task)
                future_to_task[future] = task

            # Collect results as they complete
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    # 60 second timeout per task
                    result = future.result(timeout=60)
                    results.append(result)
                    if result.success:
                        debug(f"Task {task.task_id} completed successfully")
                    else:
                        warning(f"Task {task.task_id} failed: {result.error}")
                except Exception as e:
                    error(f"Task {task.task_id} execution error: {e}")
                    results.append(TaskResult(
                        task=task,
                        error=f"Execution timeout or error: {str(e)}",
                        success=False,
                        completed_at=datetime.now()
                    ))

        debug(f"Parallel execution completed: {len(results)} results")
        return results

    def _execute_sequential(self, tasks: List[Task], previous_results: List[TaskResult]) -> List[TaskResult]:
        """Execute tasks in sequence with context passing."""
        if not tasks:
            return []

        debug(f"Starting sequential execution of {len(tasks)} tasks")
        results = []
        context = {}

        for i, task in enumerate(tasks):
            debug(
                f"Executing sequential task {i+1}/{len(tasks)}: {task.tool_name}")

            # Update task parameters with context from previous tasks
            updated_task = self._update_task_with_context(
                task, context, previous_results)

            try:
                result = self._execute_single_task(updated_task)
                results.append(result)

                if result.success:
                    debug(
                        f"Sequential task {task.task_id} completed successfully")
                    # Update context for next task
                    context = self._extract_context_from_result(
                        result, context)
                else:
                    warning(
                        f"Sequential task {task.task_id} failed: {result.error}")
                    # Stop chain execution on failure
                    break

            except Exception as e:
                error(f"Sequential task {task.task_id} execution error: {e}")
                results.append(TaskResult(
                    task=updated_task,
                    error=f"Sequential execution error: {str(e)}",
                    success=False,
                    completed_at=datetime.now()
                ))
                break  # Stop chain execution on error

        debug(f"Sequential execution completed: {len(results)} results")
        return results

    def _execute_single_task(self, task: Task) -> TaskResult:
        """Execute a single task and return the result."""
        debug(f"Executing task {task.task_id}: {task.tool_name}")

        start_time = time.time()
        started_at = datetime.now()

        try:
            # Handle legacy tool name mapping
            tool_name = task.tool_name
            if tool_name == "user_location_search":
                warning(
                    f"Legacy tool name detected: {tool_name} -> mapping to relative_search")
                tool_name = "relative_search"
                # Update the task object to use the correct name
                task.tool_name = "relative_search"

            # Load the process function
            process_function = load_function(tool_name)

            if not process_function:
                error(f"Failed to load process function: {task.tool_name}")
                return TaskResult(
                    task=task,
                    error=f"Process {task.tool_name} could not be loaded",
                    success=False,
                    started_at=started_at,
                    completed_at=datetime.now(),
                    execution_time=time.time() - start_time
                )

            # Execute the process
            result = process_function(**task.parameters)

            execution_time = time.time() - start_time
            completed_at = datetime.now()

            # Check if result indicates success
            success = not result.get('error') and result.get(
                'top_candidates') is not None

            return TaskResult(
                task=task,
                result=result,
                success=success,
                started_at=started_at,
                completed_at=completed_at,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            error(f"Task {task.task_id} execution failed: {e}")

            return TaskResult(
                task=task,
                error=str(e),
                success=False,
                started_at=started_at,
                completed_at=datetime.now(),
                execution_time=execution_time
            )

    def _build_dependency_chain(self, task: Task, all_tasks: List[Task]) -> List[Task]:
        """Build a dependency chain starting from the given task."""
        chain = []
        task_map = {t.task_id: t for t in all_tasks}

        # Find all dependencies recursively
        def add_dependencies(current_task: Task):
            for dep_id in current_task.dependencies:
                if dep_id in task_map:
                    dep_task = task_map[dep_id]
                    if dep_task not in chain:
                        add_dependencies(dep_task)  # Add dependencies first
                        chain.append(dep_task)

            # Add current task after its dependencies
            if current_task not in chain:
                chain.append(current_task)

        add_dependencies(task)
        return chain

    def _update_task_with_context(self, task: Task, context: Dict[str, Any],
                                  previous_results: List[TaskResult]) -> Task:
        """Update task parameters with context from previous tasks."""
        # Create a copy of the task with updated parameters
        updated_parameters = task.parameters.copy()

        # Update with context data
        updated_parameters.update(context)

        # For dependent tasks, look for specific context from dependency results
        if task.dependencies:
            for dep_id in task.dependencies:
                dep_result = next(
                    (r for r in previous_results if r.task.task_id == dep_id), None)
                if dep_result and dep_result.success:
                    # Extract specific context based on dependency type
                    if dep_result.task.tool_name == "poi_lookup":
                        # POI lookup provides coordinates for next task
                        if dep_result.result and 'poi_coordinates' in dep_result.result:
                            coords = dep_result.result['poi_coordinates']
                            # Store original user coordinates for distance calculations
                            updated_parameters['user_latitude'] = updated_parameters.get(
                                'latitude')
                            updated_parameters['user_longitude'] = updated_parameters.get(
                                'longitude')
                            # Update search coordinates to POI coordinates
                            updated_parameters['latitude'] = coords['latitude']
                            updated_parameters['longitude'] = coords['longitude']
                            debug(
                                f"Updated task {task.task_id} with POI coordinates: ({coords['latitude']}, {coords['longitude']}), user coordinates: ({updated_parameters['user_latitude']}, {updated_parameters['user_longitude']})")

                    elif dep_result.task.tool_name in ["boundary_search", "relative_search"]:
                        # These searches provide top candidates - use first candidate's location for next task
                        if (dep_result.result and 'top_candidates' in dep_result.result
                                and dep_result.result['top_candidates']):
                            first_candidate = dep_result.result['top_candidates'][0]
                            if hasattr(first_candidate, 'latitude') and hasattr(first_candidate, 'longitude'):
                                updated_parameters['latitude'] = first_candidate.latitude
                                updated_parameters['longitude'] = first_candidate.longitude
                                debug(
                                    f"Updated task {task.task_id} with candidate coordinates: ({first_candidate.latitude}, {first_candidate.longitude})")
                            elif isinstance(first_candidate, dict) and 'latitude' in first_candidate and 'longitude' in first_candidate:
                                updated_parameters['latitude'] = first_candidate['latitude']
                                updated_parameters['longitude'] = first_candidate['longitude']
                                debug(
                                    f"Updated task {task.task_id} with candidate coordinates: ({first_candidate['latitude']}, {first_candidate['longitude']})")

        # Create updated task
        updated_task = Task(
            task_id=task.task_id,
            tool_name=task.tool_name,
            parameters=updated_parameters,
            dependencies=task.dependencies,
            sub_request=task.sub_request,
            created_at=task.created_at
        )

        return updated_task

    def _extract_context_from_result(self, result: TaskResult, existing_context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract context information from task result for next tasks."""
        context = existing_context.copy()

        if result.success and result.result:
            # Extract coordinates from POI lookup results
            if result.task.tool_name == "poi_lookup" and 'poi_coordinates' in result.result:
                context['poi_coordinates'] = result.result['poi_coordinates']
                context['poi_location_data'] = result.result.get(
                    'top_candidates', [])

            # Extract other useful context
            if 'search_context' in result.result:
                context.update(result.result['search_context'])

        return context
